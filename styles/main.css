/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Courier New', monospace;
    background: #000;
    overflow: hidden;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

#game-container {
    position: relative;
    width: 100vw;
    height: 100vh;
}

/* Canvas */
#game-canvas {
    display: block;
    width: 100%;
    height: 100%;
    cursor: none;
}

/* Loading Screen */
#loading-screen {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #2c3e50, #3498db);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-content {
    text-align: center;
    color: white;
}

.loading-content h1 {
    font-size: 3rem;
    margin-bottom: 2rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.loading-bar {
    width: 300px;
    height: 20px;
    background: rgba(255,255,255,0.2);
    border-radius: 10px;
    overflow: hidden;
    margin: 1rem auto;
}

.loading-progress {
    height: 100%;
    background: linear-gradient(90deg, #27ae60, #2ecc71);
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 10px;
}

/* Crosshair */
#crosshair {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 100;
}

.crosshair-line {
    background: rgba(255, 255, 255, 0.8);
    position: absolute;
}

.crosshair-line.horizontal {
    width: 20px;
    height: 2px;
    top: -1px;
    left: -10px;
}

.crosshair-line.vertical {
    width: 2px;
    height: 20px;
    top: -10px;
    left: -1px;
}

/* HUD */
#hud {
    position: absolute;
    top: 20px;
    left: 20px;
    z-index: 50;
}

#debug-info {
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 10px;
    border-radius: 5px;
    font-size: 14px;
    line-height: 1.4;
}

#debug-info div {
    margin-bottom: 5px;
}

/* Hotbar */
#hotbar {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 5px;
    z-index: 50;
}

.hotbar-slot {
    width: 60px;
    height: 60px;
    background: rgba(0, 0, 0, 0.7);
    border: 2px solid #666;
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
}

.hotbar-slot:hover {
    border-color: #999;
    background: rgba(0, 0, 0, 0.8);
}

.hotbar-slot.active {
    border-color: #fff;
    background: rgba(255, 255, 255, 0.1);
}

.slot-content {
    text-align: center;
}

.block-icon {
    font-size: 24px;
    display: block;
}

.slot-number {
    position: absolute;
    bottom: 2px;
    right: 4px;
    font-size: 10px;
    color: #ccc;
}

/* Inventory Panel */
#inventory-panel {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 400px;
    max-height: 500px;
    background: rgba(0, 0, 0, 0.9);
    border: 2px solid #666;
    border-radius: 10px;
    z-index: 200;
    color: white;
}

.inventory-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #666;
}

.inventory-header h3 {
    margin: 0;
}

#close-inventory {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.inventory-grid {
    padding: 20px;
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    gap: 5px;
    max-height: 300px;
    overflow-y: auto;
}

.inventory-slot {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid #666;
    border-radius: 3px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.inventory-slot:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: #999;
}

/* Crafting Panel */
#crafting-panel {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 500px;
    background: rgba(0, 0, 0, 0.9);
    border: 2px solid #666;
    border-radius: 10px;
    z-index: 200;
    color: white;
}

.crafting-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #666;
}

.crafting-content {
    padding: 20px;
}

.crafting-grid {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
    margin-bottom: 30px;
}

.crafting-input {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 5px;
}

.crafting-slot {
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid #666;
    border-radius: 3px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

.crafting-arrow {
    font-size: 24px;
    color: #ccc;
}

.crafting-result-slot {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid #666;
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.recipe-list {
    border-top: 1px solid #666;
    padding-top: 20px;
}

.recipe-list h4 {
    margin-bottom: 15px;
    color: #ccc;
}

/* Game Menu */
#game-menu {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 300;
}

.menu-content {
    background: rgba(0, 0, 0, 0.9);
    padding: 40px;
    border-radius: 10px;
    text-align: center;
    color: white;
}

.menu-content h2 {
    margin-bottom: 30px;
    font-size: 2rem;
}

.menu-content button {
    display: block;
    width: 200px;
    margin: 10px auto;
    padding: 15px;
    background: #3498db;
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 16px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.menu-content button:hover {
    background: #2980b9;
}

/* Controls Help */
#controls-help {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 400;
}

.controls-content {
    background: rgba(0, 0, 0, 0.9);
    padding: 40px;
    border-radius: 10px;
    color: white;
    max-width: 500px;
}

.controls-content h3 {
    margin-bottom: 30px;
    text-align: center;
    font-size: 1.5rem;
}

.controls-list {
    margin-bottom: 30px;
}

.control-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding: 10px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 5px;
}

.key {
    background: #333;
    padding: 5px 10px;
    border-radius: 3px;
    font-weight: bold;
    min-width: 80px;
    text-align: center;
}

.description {
    flex: 1;
    margin-left: 20px;
}

#close-controls {
    display: block;
    width: 100%;
    padding: 15px;
    background: #e74c3c;
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 16px;
    cursor: pointer;
}

/* Instructions */
#instructions {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 500;
}

.instructions-content {
    background: rgba(0, 0, 0, 0.9);
    padding: 40px;
    border-radius: 10px;
    text-align: center;
    color: white;
    max-width: 600px;
}

.instructions-content h2 {
    margin-bottom: 20px;
    font-size: 2rem;
    color: #3498db;
}

.instructions-content p {
    margin-bottom: 15px;
    font-size: 1.1rem;
    line-height: 1.6;
}

#start-game {
    margin-top: 30px;
    padding: 15px 30px;
    background: #27ae60;
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 18px;
    cursor: pointer;
    transition: background 0.3s ease;
}

#start-game:hover {
    background: #229954;
}

/* Hidden elements */
.hidden {
    display: none !important;
}

.flex-display {
    display: flex !important;
}

.block-display {
    display: block !important;
}

/* Responsive */
@media (max-width: 768px) {
    #hotbar {
        bottom: 10px;
    }

    .hotbar-slot {
        width: 50px;
        height: 50px;
    }

    #debug-info {
        font-size: 12px;
    }

    #inventory-panel,
    #crafting-panel {
        width: 90%;
        max-width: 400px;
    }
}
