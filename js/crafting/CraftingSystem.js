/**
 * Crafting System
 * Handles recipe management and item crafting
 */

import { BlockTypes } from '../world/BlockTypes.js';

export class CraftingSystem {
    constructor(inventorySystem) {
        this.inventorySystem = inventorySystem;
        
        // Crafting grid (2x2 for simplicity)
        this.craftingGrid = new Array(4).fill(null);
        this.craftingResult = null;
        
        // Available recipes
        this.recipes = new Map();
        
        // UI elements
        this.craftingSlots = [];
        this.resultSlot = null;
        
        console.log('CraftingSystem created');
    }

    async init() {
        console.log('Initializing Crafting System...');
        
        // Initialize recipes
        this.initializeRecipes();
        
        // Setup UI
        this.setupUI();
        
        // Setup event listeners
        this.setupEventListeners();
        
        console.log('Crafting System initialized');
    }

    initializeRecipes() {
        // Wood to Planks (1:4 ratio)
        this.addRecipe('wood_to_planks', {
            pattern: [
                [4, null],
                [null, null]
            ],
            result: { id: 8, count: 4 }, // Planks
            description: '1 Wood → 4 Planks'
        });
        
        // Stone to Brick (4:1 ratio)
        this.addRecipe('stone_to_brick', {
            pattern: [
                [3, 3],
                [3, 3]
            ],
            result: { id: 7, count: 1 }, // Brick
            description: '4 Stone → 1 Brick'
        });
        
        // Sand to Glass (1:1 ratio)
        this.addRecipe('sand_to_glass', {
            pattern: [
                [5, null],
                [null, null]
            ],
            result: { id: 9, count: 1 }, // Glass
            description: '1 Sand → 1 Glass'
        });
        
        // Planks to Sticks (2:4 ratio)
        this.addRecipe('planks_to_sticks', {
            pattern: [
                [8, null],
                [8, null]
            ],
            result: { id: 16, count: 4 }, // Sticks (new item)
            description: '2 Planks → 4 Sticks'
        });
        
        // Stone and Wood to Tools (example)
        this.addRecipe('stone_pickaxe', {
            pattern: [
                [3, 3],
                [16, null]
            ],
            result: { id: 17, count: 1 }, // Stone Pickaxe (new item)
            description: '2 Stone + 1 Stick → Stone Pickaxe'
        });
        
        console.log(`Initialized ${this.recipes.size} recipes`);
    }

    addRecipe(id, recipe) {
        this.recipes.set(id, {
            id: id,
            pattern: recipe.pattern,
            result: recipe.result,
            description: recipe.description || '',
            shapeless: recipe.shapeless || false
        });
    }

    setupUI() {
        // Get crafting slots
        this.craftingSlots = Array.from(document.querySelectorAll('.crafting-slot'));
        this.resultSlot = document.querySelector('.crafting-result-slot');
        
        // Initialize slot displays
        this.updateCraftingUI();
    }

    setupEventListeners() {
        // Listen for crafting slot clicks
        this.craftingSlots.forEach((slot, index) => {
            slot.addEventListener('click', () => {
                this.handleCraftingSlotClick(index);
            });
        });
        
        // Listen for result slot clicks
        if (this.resultSlot) {
            this.resultSlot.addEventListener('click', () => {
                this.handleResultSlotClick();
            });
        }
        
        // Listen for inventory changes
        this.inventorySystem.addEventListener('itemAdded', () => {
            this.updateCraftingResult();
        });
        
        this.inventorySystem.addEventListener('itemRemoved', () => {
            this.updateCraftingResult();
        });
    }

    handleCraftingSlotClick(slotIndex) {
        // For now, just cycle through available items
        // In a full implementation, this would handle item placement from inventory
        
        const availableItems = [0, 1, 2, 3, 4, 5, 8]; // Air, Dirt, Grass, Stone, Wood, Sand, Planks
        const currentItem = this.craftingGrid[slotIndex];
        const currentIndex = availableItems.indexOf(currentItem);
        const nextIndex = (currentIndex + 1) % availableItems.length;
        
        this.craftingGrid[slotIndex] = availableItems[nextIndex];
        
        // Update UI and check for recipes
        this.updateCraftingUI();
        this.updateCraftingResult();
    }

    handleResultSlotClick() {
        if (!this.craftingResult) return;
        
        // Check if player can craft the item
        if (!this.canCraftResult()) return;
        
        // Consume ingredients
        this.consumeCraftingIngredients();
        
        // Add result to inventory
        this.inventorySystem.addItem(this.craftingResult.id, this.craftingResult.count);
        
        // Clear crafting grid
        this.clearCraftingGrid();
        
        console.log(`Crafted ${this.craftingResult.count}x ${BlockTypes.getName(this.craftingResult.id)}`);
    }

    updateCraftingUI() {
        // Update crafting slot displays
        this.craftingSlots.forEach((slot, index) => {
            const itemId = this.craftingGrid[index];
            
            if (itemId && itemId !== 0) {
                const blockType = BlockTypes.get(itemId);
                slot.innerHTML = `<span style="font-size: 24px;">${blockType.icon}</span>`;
                slot.title = blockType.name;
            } else {
                slot.innerHTML = '';
                slot.title = '';
            }
        });
        
        // Update result slot
        if (this.resultSlot) {
            if (this.craftingResult) {
                const blockType = BlockTypes.get(this.craftingResult.id);
                this.resultSlot.innerHTML = `
                    <span style="font-size: 28px;">${blockType.icon}</span>
                    ${this.craftingResult.count > 1 ? `<span style="position: absolute; bottom: 2px; right: 2px; font-size: 12px; color: white;">${this.craftingResult.count}</span>` : ''}
                `;
                this.resultSlot.title = `${blockType.name} (${this.craftingResult.count})`;
                this.resultSlot.style.position = 'relative';
                this.resultSlot.style.cursor = 'pointer';
            } else {
                this.resultSlot.innerHTML = '';
                this.resultSlot.title = '';
                this.resultSlot.style.cursor = 'default';
            }
        }
    }

    updateCraftingResult() {
        // Check all recipes to see if current grid matches
        this.craftingResult = null;
        
        for (const recipe of this.recipes.values()) {
            if (this.matchesRecipe(recipe)) {
                this.craftingResult = recipe.result;
                break;
            }
        }
        
        // Update UI
        this.updateCraftingUI();
        this.updateRecipeList();
    }

    matchesRecipe(recipe) {
        const pattern = recipe.pattern;
        
        // Check if crafting grid matches the recipe pattern
        for (let i = 0; i < 4; i++) {
            const row = Math.floor(i / 2);
            const col = i % 2;
            const expectedItem = pattern[row][col];
            const actualItem = this.craftingGrid[i];
            
            if (expectedItem !== actualItem) {
                return false;
            }
        }
        
        return true;
    }

    canCraftResult() {
        if (!this.craftingResult) return false;
        
        // Check if player has all required ingredients
        const ingredientCounts = new Map();
        
        for (const itemId of this.craftingGrid) {
            if (itemId && itemId !== 0) {
                const count = ingredientCounts.get(itemId) || 0;
                ingredientCounts.set(itemId, count + 1);
            }
        }
        
        for (const [itemId, requiredCount] of ingredientCounts) {
            if (!this.inventorySystem.hasItem(itemId, requiredCount)) {
                return false;
            }
        }
        
        return true;
    }

    consumeCraftingIngredients() {
        // Count required ingredients
        const ingredientCounts = new Map();
        
        for (const itemId of this.craftingGrid) {
            if (itemId && itemId !== 0) {
                const count = ingredientCounts.get(itemId) || 0;
                ingredientCounts.set(itemId, count + 1);
            }
        }
        
        // Remove ingredients from inventory
        for (const [itemId, count] of ingredientCounts) {
            this.inventorySystem.removeItem(itemId, count);
        }
    }

    clearCraftingGrid() {
        this.craftingGrid.fill(null);
        this.craftingResult = null;
        this.updateCraftingUI();
    }

    updateRecipeList() {
        const recipeListContent = document.getElementById('recipe-list-content');
        if (!recipeListContent) return;
        
        recipeListContent.innerHTML = '';
        
        // Show available recipes
        for (const recipe of this.recipes.values()) {
            const canCraft = this.canCraftRecipe(recipe);
            
            const recipeElement = document.createElement('div');
            recipeElement.className = 'recipe-item';
            recipeElement.style.cssText = `
                padding: 8px;
                margin: 4px 0;
                background: ${canCraft ? 'rgba(0, 255, 0, 0.1)' : 'rgba(255, 255, 255, 0.05)'};
                border: 1px solid ${canCraft ? '#00ff00' : '#666'};
                border-radius: 4px;
                cursor: pointer;
                font-size: 12px;
            `;
            
            recipeElement.innerHTML = `
                <div>${recipe.description}</div>
                <div style="font-size: 10px; color: #ccc; margin-top: 2px;">
                    ${canCraft ? 'Available' : 'Missing ingredients'}
                </div>
            `;
            
            // Click to auto-fill crafting grid
            recipeElement.addEventListener('click', () => {
                this.fillCraftingGridWithRecipe(recipe);
            });
            
            recipeListContent.appendChild(recipeElement);
        }
    }

    canCraftRecipe(recipe) {
        // Count required ingredients from pattern
        const ingredientCounts = new Map();
        
        for (const row of recipe.pattern) {
            for (const itemId of row) {
                if (itemId && itemId !== 0) {
                    const count = ingredientCounts.get(itemId) || 0;
                    ingredientCounts.set(itemId, count + 1);
                }
            }
        }
        
        // Check if player has all ingredients
        for (const [itemId, requiredCount] of ingredientCounts) {
            if (!this.inventorySystem.hasItem(itemId, requiredCount)) {
                return false;
            }
        }
        
        return true;
    }

    fillCraftingGridWithRecipe(recipe) {
        // Fill crafting grid with recipe pattern
        for (let i = 0; i < 4; i++) {
            const row = Math.floor(i / 2);
            const col = i % 2;
            this.craftingGrid[i] = recipe.pattern[row][col];
        }
        
        this.updateCraftingUI();
        this.updateCraftingResult();
    }

    // Get all available recipes
    getAllRecipes() {
        return Array.from(this.recipes.values());
    }

    // Get craftable recipes (player has ingredients)
    getCraftableRecipes() {
        return this.getAllRecipes().filter(recipe => this.canCraftRecipe(recipe));
    }

    // Debug methods
    getDebugInfo() {
        return {
            totalRecipes: this.recipes.size,
            craftableRecipes: this.getCraftableRecipes().length,
            currentGrid: this.craftingGrid.slice(),
            currentResult: this.craftingResult
        };
    }

    logRecipes() {
        console.log('=== RECIPES ===');
        for (const recipe of this.recipes.values()) {
            console.log(`${recipe.id}: ${recipe.description} (${this.canCraftRecipe(recipe) ? 'Available' : 'Unavailable'})`);
        }
        console.log('===============');
    }
}
