/**
 * Core Game Engine
 * Manages Three.js scene, renderer, camera, and coordinates all game systems
 */

import * as THREE from 'three';
import { VoxelWorld } from '../world/VoxelWorld.js';
import { PlayerController } from '../player/PlayerController.js';
import { BlockInteraction } from '../interaction/BlockInteraction.js';
import { InventorySystem } from '../inventory/InventorySystem.js';
import { CraftingSystem } from '../crafting/CraftingSystem.js';

export class GameEngine {
    constructor() {
        // Core Three.js components
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.canvas = null;
        
        // Game systems
        this.voxelWorld = null;
        this.playerController = null;
        this.blockInteraction = null;
        this.inventorySystem = null;
        this.craftingSystem = null;
        
        // Lighting
        this.ambientLight = null;
        this.directionalLight = null;
        
        // Performance tracking
        this.clock = new THREE.Clock();
        this.frameCount = 0;
        this.lastFPSUpdate = 0;
        this.fps = 0;
        
        // Game state
        this.isInitialized = false;
        this.isRunning = false;
        
        // Bind methods
        this.update = this.update.bind(this);
        this.render = this.render.bind(this);
        this.handleResize = this.handleResize.bind(this);
    }

    async init() {
        try {
            console.log('Initializing Game Engine...');
            
            // Initialize Three.js core components
            this.initRenderer();
            this.initCamera();
            this.initScene();
            this.initLighting();
            
            // Initialize game systems
            await this.initGameSystems();
            
            // Setup event listeners
            this.setupEventListeners();
            
            this.isInitialized = true;
            console.log('Game Engine initialized successfully');
            
        } catch (error) {
            console.error('Failed to initialize Game Engine:', error);
            throw error;
        }
    }

    initRenderer() {
        this.canvas = document.getElementById('game-canvas');
        if (!this.canvas) {
            throw new Error('Game canvas not found');
        }

        this.renderer = new THREE.WebGLRenderer({
            canvas: this.canvas,
            antialias: true,
            alpha: false
        });

        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
        this.renderer.setClearColor(0x87CEEB, 1); // Sky blue
        
        // Enable shadows
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        
        console.log('Renderer initialized');
    }

    initCamera() {
        const fov = 75;
        const aspect = window.innerWidth / window.innerHeight;
        const near = 0.1;
        const far = 1000;
        
        this.camera = new THREE.PerspectiveCamera(fov, aspect, near, far);
        this.camera.position.set(0, 50, 0);
        
        console.log('Camera initialized');
    }

    initScene() {
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x87CEEB); // Sky blue
        
        // Add fog for atmosphere
        this.scene.fog = new THREE.Fog(0x87CEEB, 100, 500);
        
        console.log('Scene initialized');
    }

    initLighting() {
        // Ambient light for general illumination
        this.ambientLight = new THREE.AmbientLight(0x404040, 0.4);
        this.scene.add(this.ambientLight);
        
        // Directional light (sun)
        this.directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        this.directionalLight.position.set(50, 100, 50);
        this.directionalLight.castShadow = true;
        
        // Configure shadow properties
        this.directionalLight.shadow.mapSize.width = 2048;
        this.directionalLight.shadow.mapSize.height = 2048;
        this.directionalLight.shadow.camera.near = 0.5;
        this.directionalLight.shadow.camera.far = 500;
        this.directionalLight.shadow.camera.left = -100;
        this.directionalLight.shadow.camera.right = 100;
        this.directionalLight.shadow.camera.top = 100;
        this.directionalLight.shadow.camera.bottom = -100;
        
        this.scene.add(this.directionalLight);
        
        console.log('Lighting initialized');
    }

    async initGameSystems() {
        // Initialize Voxel World
        this.voxelWorld = new VoxelWorld(this.scene);
        await this.voxelWorld.init();
        
        // Initialize Player Controller
        this.playerController = new PlayerController(this.camera, this.voxelWorld);
        await this.playerController.init();
        
        // Initialize Inventory System
        this.inventorySystem = new InventorySystem();
        await this.inventorySystem.init();

        // Initialize Crafting System
        this.craftingSystem = new CraftingSystem(this.inventorySystem);
        await this.craftingSystem.init();

        // Initialize Block Interaction
        this.blockInteraction = new BlockInteraction(
            this.camera,
            this.scene,
            this.voxelWorld,
            this.inventorySystem
        );
        await this.blockInteraction.init();
        
        console.log('Game systems initialized');
    }

    setupEventListeners() {
        window.addEventListener('resize', this.handleResize);
    }

    async start() {
        if (!this.isInitialized) {
            throw new Error('Game Engine not initialized');
        }
        
        console.log('Starting Game Engine...');
        
        // Generate initial world
        await this.voxelWorld.generateWorld();
        
        // Position player above the world
        this.playerController.setPosition(0, 60, 0);
        
        this.isRunning = true;
        console.log('Game Engine started');
    }

    update() {
        if (!this.isRunning) return;
        
        const deltaTime = this.clock.getDelta();
        
        // Update game systems
        if (this.playerController) {
            this.playerController.update(deltaTime);
        }
        
        if (this.blockInteraction) {
            this.blockInteraction.update();
        }
        
        if (this.voxelWorld) {
            this.voxelWorld.update(this.camera.position);
        }
        
        // Update performance metrics
        this.updatePerformanceMetrics();
        
        // Render the scene
        this.render();
    }

    render() {
        if (this.renderer && this.scene && this.camera) {
            this.renderer.render(this.scene, this.camera);
        }
    }

    updatePerformanceMetrics() {
        this.frameCount++;
        const currentTime = performance.now();
        
        if (currentTime - this.lastFPSUpdate >= 1000) {
            this.fps = Math.round((this.frameCount * 1000) / (currentTime - this.lastFPSUpdate));
            this.frameCount = 0;
            this.lastFPSUpdate = currentTime;
            
            // Update FPS display
            const fpsElement = document.getElementById('fps');
            if (fpsElement) {
                fpsElement.textContent = this.fps;
            }
        }
        
        // Update position display
        const positionElement = document.getElementById('position');
        if (positionElement && this.camera) {
            const pos = this.camera.position;
            positionElement.textContent = `${Math.round(pos.x)}, ${Math.round(pos.y)}, ${Math.round(pos.z)}`;
        }
    }

    handleResize() {
        if (!this.camera || !this.renderer) return;
        
        const width = window.innerWidth;
        const height = window.innerHeight;
        
        this.camera.aspect = width / height;
        this.camera.updateProjectionMatrix();
        
        this.renderer.setSize(width, height);
        this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
    }

    reset() {
        console.log('Resetting Game Engine...');
        
        this.isRunning = false;
        
        // Reset game systems
        if (this.voxelWorld) {
            this.voxelWorld.reset();
        }
        
        if (this.playerController) {
            this.playerController.reset();
        }
        
        if (this.inventorySystem) {
            this.inventorySystem.reset();
        }

        if (this.craftingSystem) {
            // Crafting system doesn't need reset as it depends on inventory
        }
        
        // Reset camera position
        this.camera.position.set(0, 50, 0);
        this.camera.rotation.set(0, 0, 0);
        
        console.log('Game Engine reset');
    }

    // Getters for other systems
    getScene() {
        return this.scene;
    }

    getCamera() {
        return this.camera;
    }

    getRenderer() {
        return this.renderer;
    }

    getVoxelWorld() {
        return this.voxelWorld;
    }

    getPlayerController() {
        return this.playerController;
    }

    getBlockInteraction() {
        return this.blockInteraction;
    }

    getInventorySystem() {
        return this.inventorySystem;
    }

    getCraftingSystem() {
        return this.craftingSystem;
    }

    // Debug methods
    enableWireframe() {
        this.scene.traverse((child) => {
            if (child.isMesh && child.material) {
                child.material.wireframe = true;
            }
        });
    }

    disableWireframe() {
        this.scene.traverse((child) => {
            if (child.isMesh && child.material) {
                child.material.wireframe = false;
            }
        });
    }
}
