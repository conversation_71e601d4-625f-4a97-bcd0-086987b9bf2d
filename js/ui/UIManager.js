/**
 * UI Manager
 * Handles all user interface interactions and updates
 */

export class UIManager {
    constructor() {
        this.isInventoryOpen = false;
        this.isCraftingOpen = false;
        this.isMenuOpen = false;
        this.isControlsOpen = false;
        this.selectedHotbarSlot = 0;
        
        // UI Elements
        this.hotbarSlots = [];
        this.inventoryPanel = null;
        this.craftingPanel = null;
        this.gameMenu = null;
        this.controlsHelp = null;
        
        // Bind methods
        this.toggleInventory = this.toggleInventory.bind(this);
        this.toggleCrafting = this.toggleCrafting.bind(this);
        this.toggleMenu = this.toggleMenu.bind(this);
        this.toggleControls = this.toggleControls.bind(this);
        this.selectHotbarSlot = this.selectHotbarSlot.bind(this);
    }

    async init() {
        console.log('Initializing UI Manager...');
        
        this.setupUIElements();
        this.setupEventListeners();
        this.initializeHotbar();
        this.initializeInventory();
        
        console.log('UI Manager initialized');
    }

    setupUIElements() {
        // Get UI elements
        this.inventoryPanel = document.getElementById('inventory-panel');
        this.craftingPanel = document.getElementById('crafting-panel');
        this.gameMenu = document.getElementById('game-menu');
        this.controlsHelp = document.getElementById('controls-help');
        
        // Get hotbar slots
        this.hotbarSlots = Array.from(document.querySelectorAll('.hotbar-slot'));
    }

    setupEventListeners() {
        // Inventory
        const closeInventoryBtn = document.getElementById('close-inventory');
        if (closeInventoryBtn) {
            closeInventoryBtn.addEventListener('click', this.toggleInventory);
        }
        
        // Crafting
        const closeCraftingBtn = document.getElementById('close-crafting');
        if (closeCraftingBtn) {
            closeCraftingBtn.addEventListener('click', this.toggleCrafting);
        }
        
        // Controls
        const showControlsBtn = document.getElementById('show-controls');
        if (showControlsBtn) {
            showControlsBtn.addEventListener('click', this.toggleControls);
        }
        
        const closeControlsBtn = document.getElementById('close-controls');
        if (closeControlsBtn) {
            closeControlsBtn.addEventListener('click', this.toggleControls);
        }
        
        // Hotbar slots
        this.hotbarSlots.forEach((slot, index) => {
            slot.addEventListener('click', () => this.selectHotbarSlot(index));
        });
    }

    initializeHotbar() {
        // Set initial hotbar selection
        this.selectHotbarSlot(0);
    }

    initializeInventory() {
        const inventoryGrid = document.getElementById('inventory-grid');
        if (!inventoryGrid) return;
        
        // Create inventory slots (8x4 = 32 slots)
        inventoryGrid.innerHTML = '';
        for (let i = 0; i < 32; i++) {
            const slot = document.createElement('div');
            slot.className = 'inventory-slot';
            slot.dataset.slot = i;
            
            slot.addEventListener('click', () => {
                this.handleInventorySlotClick(i);
            });
            
            inventoryGrid.appendChild(slot);
        }
    }

    selectHotbarSlot(index) {
        if (index < 0 || index >= this.hotbarSlots.length) return;
        
        // Remove active class from all slots
        this.hotbarSlots.forEach(slot => slot.classList.remove('active'));
        
        // Add active class to selected slot
        this.hotbarSlots[index].classList.add('active');
        
        this.selectedHotbarSlot = index;
        
        // Notify other systems about hotbar selection change
        this.onHotbarSelectionChange(index);
    }

    onHotbarSelectionChange(index) {
        // This will be called by other systems
        const event = new CustomEvent('hotbarSelectionChange', {
            detail: { selectedSlot: index }
        });
        document.dispatchEvent(event);
    }

    toggleInventory() {
        this.isInventoryOpen = !this.isInventoryOpen;
        
        if (this.inventoryPanel) {
            this.inventoryPanel.style.display = this.isInventoryOpen ? 'block' : 'none';
        }
        
        // Close other panels
        if (this.isInventoryOpen) {
            this.closeCrafting();
            this.closeMenu();
        }
        
        // Toggle pointer lock
        this.updatePointerLock();
    }

    toggleCrafting() {
        this.isCraftingOpen = !this.isCraftingOpen;
        
        if (this.craftingPanel) {
            this.craftingPanel.style.display = this.isCraftingOpen ? 'block' : 'none';
        }
        
        // Close other panels
        if (this.isCraftingOpen) {
            this.closeInventory();
            this.closeMenu();
        }
        
        // Update crafting recipes
        if (this.isCraftingOpen) {
            this.updateCraftingRecipes();
        }
        
        // Toggle pointer lock
        this.updatePointerLock();
    }

    toggleMenu() {
        this.isMenuOpen = !this.isMenuOpen;
        
        if (this.gameMenu) {
            this.gameMenu.style.display = this.isMenuOpen ? 'flex' : 'none';
        }
        
        // Close other panels
        if (this.isMenuOpen) {
            this.closeInventory();
            this.closeCrafting();
        }
        
        // Toggle pointer lock
        this.updatePointerLock();
    }

    toggleControls() {
        this.isControlsOpen = !this.isControlsOpen;
        
        if (this.controlsHelp) {
            this.controlsHelp.style.display = this.isControlsOpen ? 'flex' : 'none';
        }
    }

    closeInventory() {
        this.isInventoryOpen = false;
        if (this.inventoryPanel) {
            this.inventoryPanel.style.display = 'none';
        }
    }

    closeCrafting() {
        this.isCraftingOpen = false;
        if (this.craftingPanel) {
            this.craftingPanel.style.display = 'none';
        }
    }

    closeMenu() {
        this.isMenuOpen = false;
        if (this.gameMenu) {
            this.gameMenu.style.display = 'none';
        }
    }

    showMenu() {
        this.toggleMenu();
    }

    hideMenu() {
        this.closeMenu();
    }

    updatePointerLock() {
        const shouldLockPointer = !this.isInventoryOpen && !this.isCraftingOpen && !this.isMenuOpen;
        
        if (shouldLockPointer) {
            document.body.requestPointerLock();
        } else {
            document.exitPointerLock();
        }
    }

    handleInventorySlotClick(slotIndex) {
        console.log(`Inventory slot ${slotIndex} clicked`);
        // This will be handled by the inventory system
        const event = new CustomEvent('inventorySlotClick', {
            detail: { slotIndex }
        });
        document.dispatchEvent(event);
    }

    updateCraftingRecipes() {
        const recipeListContent = document.getElementById('recipe-list-content');
        if (!recipeListContent) return;
        
        // This will be populated by the crafting system
        recipeListContent.innerHTML = `
            <div class="recipe-item">
                <span>4x Wood → 1x Planks</span>
            </div>
            <div class="recipe-item">
                <span>4x Stone → 1x Brick</span>
            </div>
            <div class="recipe-item">
                <span>2x Planks → 1x Stick</span>
            </div>
        `;
    }

    updateHotbarItem(slotIndex, item) {
        if (slotIndex < 0 || slotIndex >= this.hotbarSlots.length) return;
        
        const slot = this.hotbarSlots[slotIndex];
        const icon = slot.querySelector('.block-icon');
        
        if (icon && item) {
            icon.textContent = item.icon || '📦';
        }
    }

    updateInventorySlot(slotIndex, item) {
        const slot = document.querySelector(`[data-slot="${slotIndex}"]`);
        if (!slot) return;
        
        if (item) {
            slot.innerHTML = `<span style="font-size: 20px;">${item.icon || '📦'}</span>`;
            slot.title = `${item.name} (${item.count || 1})`;
        } else {
            slot.innerHTML = '';
            slot.title = '';
        }
    }

    showLookingAt(blockType) {
        const lookingAtElement = document.getElementById('looking-at');
        if (lookingAtElement) {
            lookingAtElement.textContent = blockType || 'None';
        }
    }

    update() {
        // Update UI elements that need continuous updates
        // This method is called every frame
    }

    handleResize() {
        // Handle window resize events
        console.log('UI Manager handling resize');
    }

    // Utility methods
    isAnyPanelOpen() {
        return this.isInventoryOpen || this.isCraftingOpen || this.isMenuOpen;
    }

    getSelectedHotbarSlot() {
        return this.selectedHotbarSlot;
    }
}
