/**
 * Player Controller
 * Handles first-person movement, camera controls, and physics
 */

import * as THREE from 'three';

export class PlayerController {
    constructor(camera, voxelWorld) {
        this.camera = camera;
        this.voxelWorld = voxelWorld;
        
        // Player properties
        this.position = new THREE.Vector3(0, 50, 0);
        this.velocity = new THREE.Vector3(0, 0, 0);
        this.rotation = new THREE.Euler(0, 0, 0);
        
        // Movement settings
        this.walkSpeed = 5.0;
        this.runSpeed = 8.0;
        this.jumpSpeed = 8.0;
        this.mouseSensitivity = 0.002;
        
        // Physics settings
        this.gravity = -20.0;
        this.friction = 0.8;
        this.airResistance = 0.95;
        
        // Player dimensions (for collision)
        this.width = 0.6;
        this.height = 1.8;
        this.eyeHeight = 1.6;
        
        // State
        this.isOnGround = false;
        this.isRunning = false;
        this.isJumping = false;
        this.canJump = true;
        
        // Input reference (will be set by InputManager)
        this.inputManager = null;
        
        console.log('PlayerController created');
    }

    async init() {
        console.log('Initializing Player Controller...');
        
        // Set initial camera position
        this.updateCameraPosition();
        
        console.log('Player Controller initialized');
    }

    setInputManager(inputManager) {
        this.inputManager = inputManager;
    }

    update(deltaTime) {
        if (!this.inputManager) return;
        
        // Handle mouse look
        this.handleMouseLook();
        
        // Handle movement input
        this.handleMovementInput(deltaTime);
        
        // Apply physics
        this.applyPhysics(deltaTime);
        
        // Handle collisions
        this.handleCollisions();
        
        // Update camera
        this.updateCameraPosition();
    }

    handleMouseLook() {
        if (!this.inputManager.isPointerLockActive()) return;
        
        const mouseDelta = this.inputManager.getMouseDelta();
        
        // Horizontal rotation (Y-axis)
        this.rotation.y -= mouseDelta.x * this.mouseSensitivity;
        
        // Vertical rotation (X-axis)
        this.rotation.x -= mouseDelta.y * this.mouseSensitivity;
        
        // Clamp vertical rotation
        this.rotation.x = Math.max(-Math.PI / 2, Math.min(Math.PI / 2, this.rotation.x));
        
        // Reset mouse delta
        this.inputManager.resetMouseDelta();
    }

    handleMovementInput(deltaTime) {
        if (!this.inputManager.isPointerLockActive()) return;
        
        const movement = this.inputManager.getMovementInput();
        
        // Determine movement speed
        this.isRunning = this.inputManager.isKeyPressed('ShiftLeft');
        const speed = this.isRunning ? this.runSpeed : this.walkSpeed;
        
        // Calculate movement direction based on camera rotation
        const forward = new THREE.Vector3(0, 0, -1);
        const right = new THREE.Vector3(1, 0, 0);
        
        // Rotate directions based on camera Y rotation
        forward.applyAxisAngle(new THREE.Vector3(0, 1, 0), this.rotation.y);
        right.applyAxisAngle(new THREE.Vector3(0, 1, 0), this.rotation.y);
        
        // Calculate desired velocity
        const desiredVelocity = new THREE.Vector3();
        desiredVelocity.addScaledVector(forward, movement.forward * speed);
        desiredVelocity.addScaledVector(right, movement.right * speed);
        
        // Apply movement to horizontal velocity
        if (this.isOnGround) {
            this.velocity.x = desiredVelocity.x;
            this.velocity.z = desiredVelocity.z;
        } else {
            // Air control (reduced)
            this.velocity.x += (desiredVelocity.x - this.velocity.x) * 0.1;
            this.velocity.z += (desiredVelocity.z - this.velocity.z) * 0.1;
        }
        
        // Handle jumping
        if (movement.up > 0 && this.isOnGround && this.canJump) {
            this.velocity.y = this.jumpSpeed;
            this.isOnGround = false;
            this.isJumping = true;
            this.canJump = false;
        }
        
        // Reset jump flag when key is released
        if (movement.up === 0) {
            this.canJump = true;
        }
    }

    applyPhysics(deltaTime) {
        // Apply gravity
        if (!this.isOnGround) {
            this.velocity.y += this.gravity * deltaTime;
        }
        
        // Apply friction
        if (this.isOnGround) {
            this.velocity.x *= this.friction;
            this.velocity.z *= this.friction;
        } else {
            // Air resistance
            this.velocity.x *= this.airResistance;
            this.velocity.z *= this.airResistance;
        }
        
        // Update position
        this.position.addScaledVector(this.velocity, deltaTime);
    }

    handleCollisions() {
        const margin = 0.01; // Small margin to prevent floating point issues
        
        // Check ground collision
        this.checkGroundCollision(margin);
        
        // Check wall collisions
        this.checkWallCollisions(margin);
        
        // Check ceiling collision
        this.checkCeilingCollision(margin);
    }

    checkGroundCollision(margin) {
        const feetY = this.position.y;
        const blockY = Math.floor(feetY - margin);
        
        // Check blocks around player's feet
        const checkPositions = [
            { x: this.position.x - this.width/2, z: this.position.z - this.width/2 },
            { x: this.position.x + this.width/2, z: this.position.z - this.width/2 },
            { x: this.position.x - this.width/2, z: this.position.z + this.width/2 },
            { x: this.position.x + this.width/2, z: this.position.z + this.width/2 }
        ];
        
        let groundFound = false;
        let highestGround = -Infinity;
        
        for (const pos of checkPositions) {
            const blockX = Math.floor(pos.x);
            const blockZ = Math.floor(pos.z);
            
            const blockType = this.voxelWorld.getBlock(blockX, blockY, blockZ);
            if (blockType !== 0 && blockType !== 6) { // Not air or water
                groundFound = true;
                highestGround = Math.max(highestGround, blockY + 1);
            }
        }
        
        if (groundFound && this.velocity.y <= 0) {
            this.position.y = highestGround;
            this.velocity.y = 0;
            this.isOnGround = true;
            this.isJumping = false;
        } else {
            this.isOnGround = false;
        }
    }

    checkWallCollisions(margin) {
        // Check X-axis collisions
        this.checkAxisCollision('x', margin);
        
        // Check Z-axis collisions
        this.checkAxisCollision('z', margin);
    }

    checkAxisCollision(axis, margin) {
        const otherAxis = axis === 'x' ? 'z' : 'x';
        const halfWidth = this.width / 2;
        
        // Check collision in positive direction
        const posCheckPos = this.position[axis] + halfWidth + margin;
        const negCheckPos = this.position[axis] - halfWidth - margin;
        
        const checkY = [
            Math.floor(this.position.y),
            Math.floor(this.position.y + this.height - 0.1)
        ];
        
        const checkOther = [
            this.position[otherAxis] - halfWidth,
            this.position[otherAxis] + halfWidth
        ];
        
        // Check positive direction
        for (const y of checkY) {
            for (const other of checkOther) {
                const blockPos = {
                    x: axis === 'x' ? Math.floor(posCheckPos) : Math.floor(other),
                    y: y,
                    z: axis === 'z' ? Math.floor(posCheckPos) : Math.floor(other)
                };
                
                const blockType = this.voxelWorld.getBlock(blockPos.x, blockPos.y, blockPos.z);
                if (blockType !== 0 && blockType !== 6) { // Solid block
                    this.position[axis] = blockPos[axis] - halfWidth - margin;
                    this.velocity[axis] = Math.min(0, this.velocity[axis]);
                    break;
                }
            }
        }
        
        // Check negative direction
        for (const y of checkY) {
            for (const other of checkOther) {
                const blockPos = {
                    x: axis === 'x' ? Math.floor(negCheckPos) : Math.floor(other),
                    y: y,
                    z: axis === 'z' ? Math.floor(negCheckPos) : Math.floor(other)
                };
                
                const blockType = this.voxelWorld.getBlock(blockPos.x, blockPos.y, blockPos.z);
                if (blockType !== 0 && blockType !== 6) { // Solid block
                    this.position[axis] = blockPos[axis] + 1 + halfWidth + margin;
                    this.velocity[axis] = Math.max(0, this.velocity[axis]);
                    break;
                }
            }
        }
    }

    checkCeilingCollision(margin) {
        const headY = this.position.y + this.height;
        const blockY = Math.floor(headY + margin);
        
        // Check blocks around player's head
        const checkPositions = [
            { x: this.position.x - this.width/2, z: this.position.z - this.width/2 },
            { x: this.position.x + this.width/2, z: this.position.z - this.width/2 },
            { x: this.position.x - this.width/2, z: this.position.z + this.width/2 },
            { x: this.position.x + this.width/2, z: this.position.z + this.width/2 }
        ];
        
        for (const pos of checkPositions) {
            const blockX = Math.floor(pos.x);
            const blockZ = Math.floor(pos.z);
            
            const blockType = this.voxelWorld.getBlock(blockX, blockY, blockZ);
            if (blockType !== 0 && blockType !== 6) { // Solid block
                this.position.y = blockY - this.height - margin;
                this.velocity.y = Math.min(0, this.velocity.y);
                break;
            }
        }
    }

    updateCameraPosition() {
        // Set camera position to player's eye level
        this.camera.position.copy(this.position);
        this.camera.position.y += this.eyeHeight;
        
        // Set camera rotation
        this.camera.rotation.set(this.rotation.x, this.rotation.y, 0);
    }

    setPosition(x, y, z) {
        this.position.set(x, y, z);
        this.velocity.set(0, 0, 0);
        this.updateCameraPosition();
    }

    getPosition() {
        return this.position.clone();
    }

    getEyePosition() {
        const eyePos = this.position.clone();
        eyePos.y += this.eyeHeight;
        return eyePos;
    }

    getDirection() {
        const direction = new THREE.Vector3(0, 0, -1);
        direction.applyEuler(this.camera.rotation);
        return direction;
    }

    reset() {
        this.position.set(0, 50, 0);
        this.velocity.set(0, 0, 0);
        this.rotation.set(0, 0, 0);
        this.isOnGround = false;
        this.isRunning = false;
        this.isJumping = false;
        this.canJump = true;
        this.updateCameraPosition();
    }

    // Debug methods
    getDebugInfo() {
        return {
            position: this.position.clone(),
            velocity: this.velocity.clone(),
            rotation: this.rotation.clone(),
            isOnGround: this.isOnGround,
            isRunning: this.isRunning,
            isJumping: this.isJumping
        };
    }
}
