/**
 * Main entry point for the Minecraft-like game
 * Handles initialization and coordinates all game systems
 */

import { GameEngine } from './engine/GameEngine.js';
import { UIManager } from './ui/UIManager.js';
import { InputManager } from './input/InputManager.js';

class Game {
    constructor() {
        this.engine = null;
        this.uiManager = null;
        this.inputManager = null;
        this.isInitialized = false;
        this.isRunning = false;
        
        // Bind methods
        this.init = this.init.bind(this);
        this.start = this.start.bind(this);
        this.handleResize = this.handleResize.bind(this);
    }

    async init() {
        try {
            console.log('Initializing game...');
            
            // Show loading screen
            this.showLoadingProgress(10);
            
            // Initialize UI Manager
            this.uiManager = new UIManager();
            await this.uiManager.init();
            this.showLoadingProgress(30);
            
            // Initialize Game Engine
            this.engine = new GameEngine();
            await this.engine.init();
            this.showLoadingProgress(60);
            
            // Initialize Input Manager
            this.inputManager = new InputManager(this.engine, this.uiManager);
            await this.inputManager.init();

            // Connect InputManager to PlayerController
            if (this.engine.getPlayerController()) {
                this.engine.getPlayerController().setInputManager(this.inputManager);
            }

            this.showLoadingProgress(80);
            
            // Setup event listeners
            this.setupEventListeners();
            this.showLoadingProgress(90);
            
            // Final setup
            await this.finalizeSetup();
            this.showLoadingProgress(100);
            
            this.isInitialized = true;
            console.log('Game initialized successfully');
            
            // Hide loading screen after a brief delay
            setTimeout(() => {
                this.hideLoadingScreen();
            }, 500);
            
        } catch (error) {
            console.error('Failed to initialize game:', error);
            this.showError('Failed to initialize game. Please refresh the page.');
        }
    }

    showLoadingProgress(percentage) {
        const progressBar = document.querySelector('.loading-progress');
        if (progressBar) {
            progressBar.style.width = `${percentage}%`;
        }
    }

    hideLoadingScreen() {
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
            loadingScreen.style.opacity = '0';
            setTimeout(() => {
                loadingScreen.style.display = 'none';
                this.showInstructions();
            }, 300);
        }
    }

    showInstructions() {
        const instructions = document.getElementById('instructions');
        if (instructions) {
            instructions.style.display = 'flex';
        }
    }

    showError(message) {
        const loadingScreen = document.getElementById('loading-screen');
        const loadingContent = loadingScreen.querySelector('.loading-content');
        if (loadingContent) {
            loadingContent.innerHTML = `
                <h1>Error</h1>
                <p style="color: #e74c3c; margin: 20px 0;">${message}</p>
                <button onclick="location.reload()" style="padding: 10px 20px; background: #3498db; color: white; border: none; border-radius: 5px; cursor: pointer;">
                    Reload Page
                </button>
            `;
        }
    }

    async start() {
        if (!this.isInitialized) {
            console.error('Game not initialized');
            return;
        }

        try {
            console.log('Starting game...');
            
            // Hide instructions
            const instructions = document.getElementById('instructions');
            if (instructions) {
                instructions.style.display = 'none';
            }
            
            // Show game UI
            const gameUI = document.getElementById('game-ui');
            if (gameUI) {
                gameUI.style.display = 'block';
            }
            
            // Start the game engine
            await this.engine.start();
            
            // Enable input
            this.inputManager.enable();
            
            // Start the game loop
            this.isRunning = true;
            this.gameLoop();
            
            console.log('Game started successfully');
            
        } catch (error) {
            console.error('Failed to start game:', error);
            this.showError('Failed to start game. Please refresh the page.');
        }
    }

    gameLoop() {
        if (!this.isRunning) return;
        
        try {
            // Update game systems
            this.engine.update();
            this.uiManager.update();
            
            // Continue the loop
            requestAnimationFrame(() => this.gameLoop());
            
        } catch (error) {
            console.error('Error in game loop:', error);
            this.isRunning = false;
        }
    }

    pause() {
        this.isRunning = false;
        this.inputManager.disable();
        this.uiManager.showMenu();
    }

    resume() {
        this.isRunning = true;
        this.inputManager.enable();
        this.uiManager.hideMenu();
        this.gameLoop();
    }

    restart() {
        this.isRunning = false;
        this.engine.reset();
        this.start();
    }

    setupEventListeners() {
        // Window resize
        window.addEventListener('resize', this.handleResize);
        
        // Start game button
        const startButton = document.getElementById('start-game');
        if (startButton) {
            startButton.addEventListener('click', this.start);
        }
        
        // Menu buttons
        const resumeButton = document.getElementById('resume-game');
        if (resumeButton) {
            resumeButton.addEventListener('click', () => this.resume());
        }
        
        const restartButton = document.getElementById('restart-game');
        if (restartButton) {
            restartButton.addEventListener('click', () => this.restart());
        }
        
        // Visibility change (tab switching)
        document.addEventListener('visibilitychange', () => {
            if (document.hidden && this.isRunning) {
                this.pause();
            }
        });
    }

    handleResize() {
        if (this.engine) {
            this.engine.handleResize();
        }
        if (this.uiManager) {
            this.uiManager.handleResize();
        }
    }

    async finalizeSetup() {
        // Any final setup tasks
        await new Promise(resolve => setTimeout(resolve, 100));
    }
}

// Initialize and start the game when the page loads
document.addEventListener('DOMContentLoaded', async () => {
    console.log('DOM loaded, initializing game...');
    
    const game = new Game();
    
    // Make game instance globally available for debugging
    window.game = game;
    
    // Initialize the game
    await game.init();
});

// Handle any unhandled errors
window.addEventListener('error', (event) => {
    console.error('Unhandled error:', event.error);
});

window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason);
});
