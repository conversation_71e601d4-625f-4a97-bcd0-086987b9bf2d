/**
 * Input Manager
 * Handles all user input including keyboard, mouse, and touch events
 */

export class InputManager {
    constructor(gameEngine, uiManager) {
        this.gameEngine = gameEngine;
        this.uiManager = uiManager;
        
        // Input state
        this.keys = {};
        this.mouse = {
            x: 0,
            y: 0,
            deltaX: 0,
            deltaY: 0,
            leftButton: false,
            rightButton: false
        };
        
        this.isEnabled = false;
        this.isPointerLocked = false;
        
        // Bind methods
        this.onKeyDown = this.onKeyDown.bind(this);
        this.onKeyUp = this.onKeyUp.bind(this);
        this.onMouseMove = this.onMouseMove.bind(this);
        this.onMouseDown = this.onMouseDown.bind(this);
        this.onMouseUp = this.onMouseUp.bind(this);
        this.onPointerLockChange = this.onPointerLockChange.bind(this);
        this.onContextMenu = this.onContextMenu.bind(this);
        this.onWheel = this.onWheel.bind(this);
    }

    async init() {
        console.log('Initializing Input Manager...');
        
        this.setupEventListeners();
        
        console.log('Input Manager initialized');
    }

    setupEventListeners() {
        // Keyboard events
        document.addEventListener('keydown', this.onKeyDown);
        document.addEventListener('keyup', this.onKeyUp);
        
        // Mouse events
        document.addEventListener('mousemove', this.onMouseMove);
        document.addEventListener('mousedown', this.onMouseDown);
        document.addEventListener('mouseup', this.onMouseUp);
        document.addEventListener('wheel', this.onWheel);
        document.addEventListener('contextmenu', this.onContextMenu);
        
        // Pointer lock events
        document.addEventListener('pointerlockchange', this.onPointerLockChange);
        document.addEventListener('pointerlockerror', () => {
            console.error('Pointer lock error');
        });
        
        // Click to request pointer lock
        document.addEventListener('click', () => {
            if (!this.isPointerLocked && this.isEnabled && !this.uiManager.isAnyPanelOpen()) {
                document.body.requestPointerLock();
            }
        });
    }

    onKeyDown(event) {
        if (!this.isEnabled) return;
        
        const key = event.code;
        this.keys[key] = true;
        
        // Handle UI shortcuts
        if (!this.uiManager.isAnyPanelOpen()) {
            switch (key) {
                case 'KeyE':
                    event.preventDefault();
                    this.uiManager.toggleInventory();
                    break;
                case 'KeyC':
                    event.preventDefault();
                    this.uiManager.toggleCrafting();
                    break;
                case 'Escape':
                    event.preventDefault();
                    this.uiManager.toggleMenu();
                    break;
                case 'Digit1':
                    event.preventDefault();
                    this.uiManager.selectHotbarSlot(0);
                    break;
                case 'Digit2':
                    event.preventDefault();
                    this.uiManager.selectHotbarSlot(1);
                    break;
                case 'Digit3':
                    event.preventDefault();
                    this.uiManager.selectHotbarSlot(2);
                    break;
                case 'Digit4':
                    event.preventDefault();
                    this.uiManager.selectHotbarSlot(3);
                    break;
                case 'Digit5':
                    event.preventDefault();
                    this.uiManager.selectHotbarSlot(4);
                    break;
            }
        } else {
            // Handle UI navigation
            switch (key) {
                case 'Escape':
                    event.preventDefault();
                    if (this.uiManager.isInventoryOpen) {
                        this.uiManager.toggleInventory();
                    } else if (this.uiManager.isCraftingOpen) {
                        this.uiManager.toggleCrafting();
                    } else if (this.uiManager.isMenuOpen) {
                        this.uiManager.toggleMenu();
                    }
                    break;
            }
        }
    }

    onKeyUp(event) {
        if (!this.isEnabled) return;
        
        const key = event.code;
        this.keys[key] = false;
    }

    onMouseMove(event) {
        if (!this.isEnabled) return;
        
        if (this.isPointerLocked) {
            this.mouse.deltaX = event.movementX || 0;
            this.mouse.deltaY = event.movementY || 0;
        } else {
            this.mouse.x = event.clientX;
            this.mouse.y = event.clientY;
            this.mouse.deltaX = 0;
            this.mouse.deltaY = 0;
        }
    }

    onMouseDown(event) {
        if (!this.isEnabled) return;
        
        switch (event.button) {
            case 0: // Left button
                this.mouse.leftButton = true;
                if (this.isPointerLocked) {
                    event.preventDefault();
                    this.handleLeftClick();
                }
                break;
            case 2: // Right button
                this.mouse.rightButton = true;
                if (this.isPointerLocked) {
                    event.preventDefault();
                    this.handleRightClick();
                }
                break;
        }
    }

    onMouseUp(event) {
        if (!this.isEnabled) return;
        
        switch (event.button) {
            case 0: // Left button
                this.mouse.leftButton = false;
                break;
            case 2: // Right button
                this.mouse.rightButton = false;
                break;
        }
    }

    onWheel(event) {
        if (!this.isEnabled || !this.isPointerLocked) return;
        
        event.preventDefault();
        
        // Scroll through hotbar
        const direction = event.deltaY > 0 ? 1 : -1;
        const currentSlot = this.uiManager.getSelectedHotbarSlot();
        const newSlot = (currentSlot + direction + 5) % 5; // 5 hotbar slots
        
        this.uiManager.selectHotbarSlot(newSlot);
    }

    onContextMenu(event) {
        // Prevent right-click context menu
        event.preventDefault();
    }

    onPointerLockChange() {
        this.isPointerLocked = document.pointerLockElement === document.body;
        
        if (this.isPointerLocked) {
            console.log('Pointer locked');
        } else {
            console.log('Pointer unlocked');
        }
    }

    handleLeftClick() {
        // Break block
        if (this.gameEngine && this.gameEngine.getBlockInteraction()) {
            this.gameEngine.getBlockInteraction().breakBlock();
        }
    }

    handleRightClick() {
        // Place block
        if (this.gameEngine && this.gameEngine.getBlockInteraction()) {
            const selectedSlot = this.uiManager.getSelectedHotbarSlot();
            this.gameEngine.getBlockInteraction().placeBlock(selectedSlot);
        }
    }

    enable() {
        this.isEnabled = true;
        console.log('Input Manager enabled');
    }

    disable() {
        this.isEnabled = false;
        this.keys = {};
        this.mouse.leftButton = false;
        this.mouse.rightButton = false;
        
        // Exit pointer lock
        if (this.isPointerLocked) {
            document.exitPointerLock();
        }
        
        console.log('Input Manager disabled');
    }

    // Getters for other systems
    isKeyPressed(keyCode) {
        return !!this.keys[keyCode];
    }

    getMouseDelta() {
        return {
            x: this.mouse.deltaX,
            y: this.mouse.deltaY
        };
    }

    isMouseButtonPressed(button) {
        switch (button) {
            case 0:
                return this.mouse.leftButton;
            case 2:
                return this.mouse.rightButton;
            default:
                return false;
        }
    }

    getMousePosition() {
        return {
            x: this.mouse.x,
            y: this.mouse.y
        };
    }

    isPointerLockActive() {
        return this.isPointerLocked;
    }

    // Movement input helpers
    getMovementInput() {
        const movement = {
            forward: 0,
            right: 0,
            up: 0
        };

        if (this.isKeyPressed('KeyW')) movement.forward += 1;
        if (this.isKeyPressed('KeyS')) movement.forward -= 1;
        if (this.isKeyPressed('KeyD')) movement.right += 1;
        if (this.isKeyPressed('KeyA')) movement.right -= 1;
        if (this.isKeyPressed('Space')) movement.up += 1;
        if (this.isKeyPressed('ShiftLeft')) movement.up -= 1;

        return movement;
    }

    // Reset mouse delta after use
    resetMouseDelta() {
        this.mouse.deltaX = 0;
        this.mouse.deltaY = 0;
    }
}
