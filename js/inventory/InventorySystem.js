/**
 * Inventory System
 * Manages player inventory, items, and storage
 */

import { BlockTypes } from '../world/BlockTypes.js';

export class InventorySystem {
    constructor() {
        // Inventory configuration
        this.maxSlots = 32; // Total inventory slots
        this.hotbarSlots = 5; // Number of hotbar slots
        
        // Inventory data
        this.items = new Map(); // Map of item ID to item data
        this.slots = new Array(this.maxSlots).fill(null); // Slot array
        
        // Hotbar items (references to main inventory)
        this.hotbar = new Array(this.hotbarSlots).fill(null);
        
        // Event listeners
        this.eventListeners = new Map();
        
        console.log('InventorySystem created');
    }

    async init() {
        console.log('Initializing Inventory System...');
        
        // Initialize with starting items
        this.initializeStartingItems();
        
        // Setup event listeners
        this.setupEventListeners();
        
        // Update UI
        this.updateUI();
        
        console.log('Inventory System initialized');
    }

    initializeStartingItems() {
        // Give player some starting blocks
        this.addItem(1, 64); // Dirt
        this.addItem(2, 32); // Grass
        this.addItem(3, 16); // Stone
        this.addItem(4, 8);  // Wood
        this.addItem(5, 16); // Sand
        
        // Setup initial hotbar
        this.setupInitialHotbar();
    }

    setupInitialHotbar() {
        // Put basic blocks in hotbar
        const hotbarItems = [1, 2, 3, 4, 5]; // Dirt, Grass, Stone, Wood, Sand
        
        for (let i = 0; i < this.hotbarSlots && i < hotbarItems.length; i++) {
            const itemId = hotbarItems[i];
            if (this.hasItem(itemId)) {
                this.hotbar[i] = {
                    id: itemId,
                    count: this.getItemCount(itemId),
                    maxStack: this.getMaxStackSize(itemId)
                };
            }
        }
    }

    setupEventListeners() {
        // Listen for inventory slot clicks
        document.addEventListener('inventorySlotClick', (event) => {
            this.handleInventorySlotClick(event.detail.slotIndex);
        });
        
        // Listen for hotbar selection changes
        document.addEventListener('hotbarSelectionChange', (event) => {
            this.handleHotbarSelectionChange(event.detail.selectedSlot);
        });
    }

    addItem(itemId, count = 1) {
        if (count <= 0) return false;
        
        const blockType = BlockTypes.get(itemId);
        if (!blockType) return false;
        
        // Check if item already exists
        if (this.items.has(itemId)) {
            const existingItem = this.items.get(itemId);
            existingItem.count += count;
        } else {
            // Create new item entry
            this.items.set(itemId, {
                id: itemId,
                name: blockType.name,
                icon: blockType.icon,
                count: count,
                maxStack: this.getMaxStackSize(itemId)
            });
        }
        
        // Update slots
        this.updateSlots();
        
        // Update UI
        this.updateUI();
        
        // Trigger event
        this.triggerEvent('itemAdded', { itemId, count });
        
        return true;
    }

    removeItem(itemId, count = 1) {
        if (count <= 0) return false;
        
        if (!this.items.has(itemId)) return false;
        
        const item = this.items.get(itemId);
        if (item.count < count) return false;
        
        item.count -= count;
        
        // Remove item if count reaches zero
        if (item.count <= 0) {
            this.items.delete(itemId);
        }
        
        // Update slots
        this.updateSlots();
        
        // Update UI
        this.updateUI();
        
        // Trigger event
        this.triggerEvent('itemRemoved', { itemId, count });
        
        return true;
    }

    hasItem(itemId, count = 1) {
        const item = this.items.get(itemId);
        return item && item.count >= count;
    }

    getItemCount(itemId) {
        const item = this.items.get(itemId);
        return item ? item.count : 0;
    }

    getMaxStackSize(itemId) {
        // Most blocks stack to 64
        return 64;
    }

    updateSlots() {
        // Clear slots
        this.slots.fill(null);
        
        // Fill slots with items
        let slotIndex = 0;
        for (const [itemId, item] of this.items) {
            if (slotIndex >= this.maxSlots) break;
            
            let remainingCount = item.count;
            const maxStack = item.maxStack;
            
            while (remainingCount > 0 && slotIndex < this.maxSlots) {
                const stackSize = Math.min(remainingCount, maxStack);
                
                this.slots[slotIndex] = {
                    id: itemId,
                    name: item.name,
                    icon: item.icon,
                    count: stackSize,
                    maxStack: maxStack
                };
                
                remainingCount -= stackSize;
                slotIndex++;
            }
        }
    }

    updateUI() {
        // Update hotbar UI
        this.updateHotbarUI();
        
        // Update inventory UI
        this.updateInventoryUI();
    }

    updateHotbarUI() {
        const hotbarSlots = document.querySelectorAll('.hotbar-slot');
        
        hotbarSlots.forEach((slotElement, index) => {
            const icon = slotElement.querySelector('.block-icon');
            const slot = this.slots[index];
            
            if (slot && icon) {
                icon.textContent = slot.icon;
                slotElement.title = `${slot.name} (${slot.count})`;
            } else if (icon) {
                icon.textContent = '';
                slotElement.title = '';
            }
        });
    }

    updateInventoryUI() {
        const inventorySlots = document.querySelectorAll('.inventory-slot');
        
        inventorySlots.forEach((slotElement, index) => {
            const slot = this.slots[index];
            
            if (slot) {
                slotElement.innerHTML = `
                    <span style="font-size: 20px;">${slot.icon}</span>
                    ${slot.count > 1 ? `<span style="position: absolute; bottom: 2px; right: 2px; font-size: 10px; color: white;">${slot.count}</span>` : ''}
                `;
                slotElement.title = `${slot.name} (${slot.count})`;
                slotElement.style.position = 'relative';
            } else {
                slotElement.innerHTML = '';
                slotElement.title = '';
            }
        });
    }

    handleInventorySlotClick(slotIndex) {
        const slot = this.slots[slotIndex];
        if (!slot) return;
        
        console.log(`Clicked inventory slot ${slotIndex}: ${slot.name} (${slot.count})`);
        
        // TODO: Implement item moving/splitting logic
    }

    handleHotbarSelectionChange(selectedSlot) {
        console.log(`Hotbar selection changed to slot ${selectedSlot}`);
        
        // Update hotbar reference
        this.updateHotbarReference(selectedSlot);
    }

    updateHotbarReference(selectedSlot) {
        // Update hotbar array to reflect current inventory state
        for (let i = 0; i < this.hotbarSlots; i++) {
            const slot = this.slots[i];
            this.hotbar[i] = slot ? {
                id: slot.id,
                count: slot.count,
                maxStack: slot.maxStack
            } : null;
        }
    }

    getHotbarItem(slot) {
        if (slot < 0 || slot >= this.hotbarSlots) return null;
        return this.slots[slot];
    }

    getSelectedHotbarItem(selectedSlot) {
        return this.getHotbarItem(selectedSlot);
    }

    // Crafting support methods
    canCraft(recipe) {
        if (!recipe || !recipe.ingredients) return false;
        
        for (const ingredient of recipe.ingredients) {
            if (!this.hasItem(ingredient.id, ingredient.count)) {
                return false;
            }
        }
        
        return true;
    }

    consumeRecipeIngredients(recipe) {
        if (!this.canCraft(recipe)) return false;
        
        for (const ingredient of recipe.ingredients) {
            this.removeItem(ingredient.id, ingredient.count);
        }
        
        return true;
    }

    // Utility methods
    getInventoryData() {
        return {
            items: Array.from(this.items.entries()),
            slots: this.slots.slice(),
            hotbar: this.hotbar.slice()
        };
    }

    loadInventoryData(data) {
        if (!data) return;
        
        // Clear current inventory
        this.items.clear();
        this.slots.fill(null);
        this.hotbar.fill(null);
        
        // Load items
        if (data.items) {
            for (const [itemId, itemData] of data.items) {
                this.items.set(itemId, itemData);
            }
        }
        
        // Load slots
        if (data.slots) {
            this.slots = data.slots.slice();
        }
        
        // Load hotbar
        if (data.hotbar) {
            this.hotbar = data.hotbar.slice();
        }
        
        // Update UI
        this.updateUI();
    }

    reset() {
        console.log('Resetting Inventory System...');
        
        // Clear all data
        this.items.clear();
        this.slots.fill(null);
        this.hotbar.fill(null);
        
        // Reinitialize with starting items
        this.initializeStartingItems();
        
        console.log('Inventory System reset');
    }

    // Event system
    addEventListener(event, callback) {
        if (!this.eventListeners.has(event)) {
            this.eventListeners.set(event, []);
        }
        this.eventListeners.get(event).push(callback);
    }

    removeEventListener(event, callback) {
        if (this.eventListeners.has(event)) {
            const listeners = this.eventListeners.get(event);
            const index = listeners.indexOf(callback);
            if (index > -1) {
                listeners.splice(index, 1);
            }
        }
    }

    triggerEvent(event, data) {
        if (this.eventListeners.has(event)) {
            const listeners = this.eventListeners.get(event);
            for (const callback of listeners) {
                callback(data);
            }
        }
    }

    // Debug methods
    getDebugInfo() {
        return {
            totalItems: this.items.size,
            totalSlots: this.maxSlots,
            usedSlots: this.slots.filter(slot => slot !== null).length,
            hotbarItems: this.hotbar.filter(item => item !== null).length,
            items: Array.from(this.items.entries())
        };
    }

    logInventory() {
        console.log('=== INVENTORY ===');
        for (const [itemId, item] of this.items) {
            console.log(`${item.name}: ${item.count}`);
        }
        console.log('================');
    }
}
