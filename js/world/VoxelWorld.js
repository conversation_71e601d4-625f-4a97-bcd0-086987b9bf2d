/**
 * Voxel World System
 * Manages the block-based world with efficient chunk rendering
 */

import * as THREE from 'three';
import { BlockTypes } from './BlockTypes.js';
import { Chunk } from './Chunk.js';
import { WorldGenerator } from './WorldGenerator.js';

export class VoxelWorld {
    constructor(scene) {
        this.scene = scene;
        
        // World configuration
        this.chunkSize = 16; // 16x16x16 blocks per chunk
        this.worldHeight = 128; // Maximum world height
        this.renderDistance = 4; // Chunks to render around player
        
        // World data
        this.chunks = new Map(); // Map of chunk coordinates to Chunk objects
        this.loadedChunks = new Set(); // Set of loaded chunk keys
        
        // World generator
        this.worldGenerator = new WorldGenerator();
        
        // Materials
        this.materials = new Map();
        
        // Player position tracking
        this.lastPlayerChunk = { x: 0, z: 0 };
        
        console.log('VoxelWorld created');
    }

    async init() {
        console.log('Initializing Voxel World...');
        
        // Initialize block types
        await BlockTypes.init();
        
        // Initialize world generator
        await this.worldGenerator.init();
        
        // Create materials for different block types
        this.createMaterials();
        
        console.log('Voxel World initialized');
    }

    createMaterials() {
        const blockTypes = BlockTypes.getAll();
        
        for (const [blockId, blockType] of blockTypes) {
            const material = new THREE.MeshLambertMaterial({
                color: blockType.color,
                transparent: blockType.transparent || false,
                opacity: blockType.opacity || 1.0
            });
            
            this.materials.set(blockId, material);
        }
        
        console.log(`Created ${this.materials.size} block materials`);
    }

    async generateWorld() {
        console.log('Generating initial world...');
        
        // Generate chunks around spawn point (0, 0)
        const spawnChunkX = 0;
        const spawnChunkZ = 0;
        
        for (let x = spawnChunkX - this.renderDistance; x <= spawnChunkX + this.renderDistance; x++) {
            for (let z = spawnChunkZ - this.renderDistance; z <= spawnChunkZ + this.renderDistance; z++) {
                await this.loadChunk(x, z);
            }
        }
        
        console.log('Initial world generated');
    }

    async loadChunk(chunkX, chunkZ) {
        const chunkKey = `${chunkX},${chunkZ}`;
        
        if (this.loadedChunks.has(chunkKey)) {
            return this.chunks.get(chunkKey);
        }
        
        // Create new chunk
        const chunk = new Chunk(chunkX, chunkZ, this.chunkSize, this.worldHeight);
        
        // Generate chunk data
        await this.worldGenerator.generateChunk(chunk);
        
        // Create chunk mesh
        this.createChunkMesh(chunk);
        
        // Store chunk
        this.chunks.set(chunkKey, chunk);
        this.loadedChunks.add(chunkKey);
        
        return chunk;
    }

    unloadChunk(chunkX, chunkZ) {
        const chunkKey = `${chunkX},${chunkZ}`;
        
        if (!this.loadedChunks.has(chunkKey)) {
            return;
        }
        
        const chunk = this.chunks.get(chunkKey);
        if (chunk && chunk.mesh) {
            this.scene.remove(chunk.mesh);
            chunk.mesh.geometry.dispose();
            chunk.mesh = null;
        }
        
        this.chunks.delete(chunkKey);
        this.loadedChunks.delete(chunkKey);
    }

    createChunkMesh(chunk) {
        const geometry = new THREE.BufferGeometry();
        const positions = [];
        const normals = [];
        const uvs = [];
        const colors = [];
        const indices = [];
        
        let vertexIndex = 0;
        
        // Iterate through all blocks in the chunk
        for (let x = 0; x < this.chunkSize; x++) {
            for (let y = 0; y < this.worldHeight; y++) {
                for (let z = 0; z < this.chunkSize; z++) {
                    const blockType = chunk.getBlock(x, y, z);
                    
                    if (blockType === 0) continue; // Skip air blocks
                    
                    const worldX = chunk.x * this.chunkSize + x;
                    const worldY = y;
                    const worldZ = chunk.z * this.chunkSize + z;
                    
                    // Check each face of the block
                    const faces = [
                        { dir: [0, 1, 0], corners: [[0, 1, 1], [1, 1, 1], [1, 1, 0], [0, 1, 0]] }, // top
                        { dir: [0, -1, 0], corners: [[0, 0, 0], [1, 0, 0], [1, 0, 1], [0, 0, 1]] }, // bottom
                        { dir: [1, 0, 0], corners: [[1, 0, 1], [1, 1, 1], [1, 1, 0], [1, 0, 0]] }, // right
                        { dir: [-1, 0, 0], corners: [[0, 0, 0], [0, 1, 0], [0, 1, 1], [0, 0, 1]] }, // left
                        { dir: [0, 0, 1], corners: [[1, 0, 1], [0, 0, 1], [0, 1, 1], [1, 1, 1]] }, // front
                        { dir: [0, 0, -1], corners: [[0, 0, 0], [1, 0, 0], [1, 1, 0], [0, 1, 0]] }  // back
                    ];
                    
                    for (const face of faces) {
                        const [dx, dy, dz] = face.dir;
                        const neighborX = x + dx;
                        const neighborY = y + dy;
                        const neighborZ = z + dz;
                        
                        // Check if face should be rendered (no adjacent block)
                        if (this.shouldRenderFace(chunk, neighborX, neighborY, neighborZ)) {
                            // Add face vertices
                            const faceVertices = face.corners.map(corner => [
                                worldX + corner[0],
                                worldY + corner[1],
                                worldZ + corner[2]
                            ]);
                            
                            // Add positions
                            for (const vertex of faceVertices) {
                                positions.push(...vertex);
                            }
                            
                            // Add normals
                            for (let i = 0; i < 4; i++) {
                                normals.push(...face.dir);
                            }
                            
                            // Add UVs
                            uvs.push(0, 0, 1, 0, 1, 1, 0, 1);
                            
                            // Add colors based on block type
                            const blockInfo = BlockTypes.get(blockType);
                            const color = new THREE.Color(blockInfo.color);
                            for (let i = 0; i < 4; i++) {
                                colors.push(color.r, color.g, color.b);
                            }
                            
                            // Add indices for two triangles
                            indices.push(
                                vertexIndex, vertexIndex + 1, vertexIndex + 2,
                                vertexIndex, vertexIndex + 2, vertexIndex + 3
                            );
                            
                            vertexIndex += 4;
                        }
                    }
                }
            }
        }
        
        // Set geometry attributes
        geometry.setAttribute('position', new THREE.Float32BufferAttribute(positions, 3));
        geometry.setAttribute('normal', new THREE.Float32BufferAttribute(normals, 3));
        geometry.setAttribute('uv', new THREE.Float32BufferAttribute(uvs, 2));
        geometry.setAttribute('color', new THREE.Float32BufferAttribute(colors, 3));
        geometry.setIndex(indices);
        
        // Create material
        const material = new THREE.MeshLambertMaterial({
            vertexColors: true,
            side: THREE.FrontSide
        });
        
        // Create mesh
        chunk.mesh = new THREE.Mesh(geometry, material);
        chunk.mesh.castShadow = true;
        chunk.mesh.receiveShadow = true;
        
        // Add to scene
        this.scene.add(chunk.mesh);
    }

    shouldRenderFace(chunk, x, y, z) {
        // Check bounds
        if (y < 0 || y >= this.worldHeight) {
            return y >= this.worldHeight; // Render top faces at world height
        }
        
        // Check within chunk bounds
        if (x >= 0 && x < this.chunkSize && z >= 0 && z < this.chunkSize) {
            return chunk.getBlock(x, y, z) === 0; // Render if neighbor is air
        }
        
        // Check neighboring chunks (simplified - assume air for now)
        return true;
    }

    update(playerPosition) {
        // Calculate player's chunk position
        const playerChunkX = Math.floor(playerPosition.x / this.chunkSize);
        const playerChunkZ = Math.floor(playerPosition.z / this.chunkSize);
        
        // Check if player moved to a different chunk
        if (playerChunkX !== this.lastPlayerChunk.x || playerChunkZ !== this.lastPlayerChunk.z) {
            this.updateChunksAroundPlayer(playerChunkX, playerChunkZ);
            this.lastPlayerChunk = { x: playerChunkX, z: playerChunkZ };
        }
    }

    async updateChunksAroundPlayer(playerChunkX, playerChunkZ) {
        // Load chunks within render distance
        const chunksToLoad = [];
        for (let x = playerChunkX - this.renderDistance; x <= playerChunkX + this.renderDistance; x++) {
            for (let z = playerChunkZ - this.renderDistance; z <= playerChunkZ + this.renderDistance; z++) {
                const chunkKey = `${x},${z}`;
                if (!this.loadedChunks.has(chunkKey)) {
                    chunksToLoad.push({ x, z });
                }
            }
        }
        
        // Unload chunks outside render distance
        const chunksToUnload = [];
        for (const chunkKey of this.loadedChunks) {
            const [x, z] = chunkKey.split(',').map(Number);
            const distance = Math.max(Math.abs(x - playerChunkX), Math.abs(z - playerChunkZ));
            if (distance > this.renderDistance) {
                chunksToUnload.push({ x, z });
            }
        }
        
        // Unload distant chunks
        for (const chunk of chunksToUnload) {
            this.unloadChunk(chunk.x, chunk.z);
        }
        
        // Load new chunks
        for (const chunk of chunksToLoad) {
            await this.loadChunk(chunk.x, chunk.z);
        }
    }

    getBlock(worldX, worldY, worldZ) {
        if (worldY < 0 || worldY >= this.worldHeight) {
            return 0; // Air
        }
        
        const chunkX = Math.floor(worldX / this.chunkSize);
        const chunkZ = Math.floor(worldZ / this.chunkSize);
        const chunkKey = `${chunkX},${chunkZ}`;
        
        const chunk = this.chunks.get(chunkKey);
        if (!chunk) {
            return 0; // Air if chunk not loaded
        }
        
        const localX = worldX - chunkX * this.chunkSize;
        const localZ = worldZ - chunkZ * this.chunkSize;
        
        return chunk.getBlock(localX, worldY, localZ);
    }

    setBlock(worldX, worldY, worldZ, blockType) {
        if (worldY < 0 || worldY >= this.worldHeight) {
            return false;
        }
        
        const chunkX = Math.floor(worldX / this.chunkSize);
        const chunkZ = Math.floor(worldZ / this.chunkSize);
        const chunkKey = `${chunkX},${chunkZ}`;
        
        const chunk = this.chunks.get(chunkKey);
        if (!chunk) {
            return false; // Can't set block in unloaded chunk
        }
        
        const localX = worldX - chunkX * this.chunkSize;
        const localZ = worldZ - chunkZ * this.chunkSize;
        
        chunk.setBlock(localX, worldY, localZ, blockType);
        
        // Regenerate chunk mesh
        this.regenerateChunkMesh(chunk);
        
        return true;
    }

    regenerateChunkMesh(chunk) {
        // Remove old mesh
        if (chunk.mesh) {
            this.scene.remove(chunk.mesh);
            chunk.mesh.geometry.dispose();
        }
        
        // Create new mesh
        this.createChunkMesh(chunk);
    }

    reset() {
        console.log('Resetting Voxel World...');
        
        // Remove all chunks
        for (const chunk of this.chunks.values()) {
            if (chunk.mesh) {
                this.scene.remove(chunk.mesh);
                chunk.mesh.geometry.dispose();
            }
        }
        
        this.chunks.clear();
        this.loadedChunks.clear();
        this.lastPlayerChunk = { x: 0, z: 0 };
        
        console.log('Voxel World reset');
    }
}
