/**
 * World Generator
 * Generates terrain and structures for chunks
 */

import { BlockTypes } from './BlockTypes.js';

export class WorldGenerator {
    constructor() {
        this.seed = Math.random() * 1000000;
        this.seaLevel = 32;
        this.maxHeight = 64;
        this.minHeight = 16;
        
        // Noise parameters
        this.terrainScale = 0.02;
        this.terrainAmplitude = 20;
        this.caveScale = 0.05;
        this.caveThreshold = 0.6;
        
        console.log(`WorldGenerator created with seed: ${this.seed}`);
    }

    async init() {
        console.log('Initializing World Generator...');
        // Any async initialization if needed
        console.log('World Generator initialized');
    }

    async generateChunk(chunk) {
        console.log(`Generating chunk at (${chunk.x}, ${chunk.z})`);
        
        // Generate terrain height map
        const heightMap = this.generateHeightMap(chunk);
        
        // Generate basic terrain
        this.generateTerrain(chunk, heightMap);
        
        // Generate caves
        this.generateCaves(chunk);
        
        // Generate ores
        this.generateOres(chunk);
        
        // Generate trees
        this.generateTrees(chunk, heightMap);
        
        // Mark chunk as generated
        chunk.markAsGenerated();
        
        console.log(`Chunk (${chunk.x}, ${chunk.z}) generated`);
    }

    generateHeightMap(chunk) {
        const heightMap = [];
        
        for (let x = 0; x < chunk.size; x++) {
            heightMap[x] = [];
            for (let z = 0; z < chunk.size; z++) {
                const worldX = chunk.x * chunk.size + x;
                const worldZ = chunk.z * chunk.size + z;
                
                // Generate height using noise
                const height = this.getTerrainHeight(worldX, worldZ);
                heightMap[x][z] = Math.floor(height);
            }
        }
        
        return heightMap;
    }

    getTerrainHeight(x, z) {
        // Simple noise function for terrain generation
        const noise1 = this.noise(x * this.terrainScale, z * this.terrainScale) * this.terrainAmplitude;
        const noise2 = this.noise(x * this.terrainScale * 2, z * this.terrainScale * 2) * (this.terrainAmplitude * 0.5);
        const noise3 = this.noise(x * this.terrainScale * 4, z * this.terrainScale * 4) * (this.terrainAmplitude * 0.25);
        
        const height = this.seaLevel + noise1 + noise2 + noise3;
        return Math.max(this.minHeight, Math.min(this.maxHeight, height));
    }

    generateTerrain(chunk, heightMap) {
        for (let x = 0; x < chunk.size; x++) {
            for (let z = 0; z < chunk.size; z++) {
                const height = heightMap[x][z];
                
                for (let y = 0; y < chunk.height; y++) {
                    let blockType = 0; // Air
                    
                    if (y <= height) {
                        if (y === height) {
                            // Surface block
                            if (height > this.seaLevel + 5) {
                                blockType = 2; // Grass
                            } else if (height > this.seaLevel) {
                                blockType = 5; // Sand
                            } else {
                                blockType = 1; // Dirt (underwater)
                            }
                        } else if (y > height - 4) {
                            // Subsurface
                            blockType = 1; // Dirt
                        } else {
                            // Deep underground
                            blockType = 3; // Stone
                        }
                    } else if (y <= this.seaLevel) {
                        // Water
                        blockType = 6; // Water
                    }
                    
                    chunk.setBlock(x, y, z, blockType);
                }
            }
        }
    }

    generateCaves(chunk) {
        for (let x = 0; x < chunk.size; x++) {
            for (let y = 1; y < chunk.height - 1; y++) {
                for (let z = 0; z < chunk.size; z++) {
                    const worldX = chunk.x * chunk.size + x;
                    const worldZ = chunk.z * chunk.size + z;
                    
                    // Only generate caves in stone
                    if (chunk.getBlock(x, y, z) === 3) {
                        const caveNoise = this.noise3D(
                            worldX * this.caveScale,
                            y * this.caveScale,
                            worldZ * this.caveScale
                        );
                        
                        if (caveNoise > this.caveThreshold) {
                            chunk.setBlock(x, y, z, 0); // Air (cave)
                        }
                    }
                }
            }
        }
    }

    generateOres(chunk) {
        // Generate coal
        this.generateOreVeins(chunk, 10, 0.02, 5, 40, 3, 6); // Coal
        
        // Generate iron
        this.generateOreVeins(chunk, 11, 0.015, 3, 35, 2, 4); // Iron
        
        // Generate gold
        this.generateOreVeins(chunk, 12, 0.01, 2, 25, 1, 3); // Gold
        
        // Generate diamond
        this.generateOreVeins(chunk, 13, 0.005, 1, 15, 1, 2); // Diamond
    }

    generateOreVeins(chunk, oreType, probability, minY, maxY, minSize, maxSize) {
        for (let x = 0; x < chunk.size; x++) {
            for (let y = minY; y < Math.min(maxY, chunk.height); y++) {
                for (let z = 0; z < chunk.size; z++) {
                    if (chunk.getBlock(x, y, z) === 3) { // Only replace stone
                        const worldX = chunk.x * chunk.size + x;
                        const worldZ = chunk.z * chunk.size + z;
                        
                        if (this.random(worldX, y, worldZ) < probability) {
                            // Generate ore vein
                            const veinSize = Math.floor(this.random(worldX + 1, y + 1, worldZ + 1) * (maxSize - minSize + 1)) + minSize;
                            this.generateOreVein(chunk, x, y, z, oreType, veinSize);
                        }
                    }
                }
            }
        }
    }

    generateOreVein(chunk, startX, startY, startZ, oreType, size) {
        const positions = [{ x: startX, y: startY, z: startZ }];
        
        for (let i = 0; i < size; i++) {
            if (positions.length === 0) break;
            
            const pos = positions.shift();
            
            if (chunk.isValidCoordinate(pos.x, pos.y, pos.z) && chunk.getBlock(pos.x, pos.y, pos.z) === 3) {
                chunk.setBlock(pos.x, pos.y, pos.z, oreType);
                
                // Add neighboring positions
                const neighbors = [
                    { x: pos.x + 1, y: pos.y, z: pos.z },
                    { x: pos.x - 1, y: pos.y, z: pos.z },
                    { x: pos.x, y: pos.y + 1, z: pos.z },
                    { x: pos.x, y: pos.y - 1, z: pos.z },
                    { x: pos.x, y: pos.y, z: pos.z + 1 },
                    { x: pos.x, y: pos.y, z: pos.z - 1 }
                ];
                
                for (const neighbor of neighbors) {
                    if (this.random(neighbor.x * 1000, neighbor.y * 1000, neighbor.z * 1000) < 0.6) {
                        positions.push(neighbor);
                    }
                }
            }
        }
    }

    generateTrees(chunk, heightMap) {
        for (let x = 2; x < chunk.size - 2; x++) {
            for (let z = 2; z < chunk.size - 2; z++) {
                const height = heightMap[x][z];
                const worldX = chunk.x * chunk.size + x;
                const worldZ = chunk.z * chunk.size + z;
                
                // Only generate trees on grass
                if (chunk.getBlock(x, height, z) === 2 && height > this.seaLevel + 2) {
                    if (this.random(worldX, height, worldZ) < 0.05) { // 5% chance
                        this.generateTree(chunk, x, height + 1, z);
                    }
                }
            }
        }
    }

    generateTree(chunk, x, y, z) {
        const treeHeight = 4 + Math.floor(this.random(x * 100, y * 100, z * 100) * 3); // 4-6 blocks tall
        
        // Generate trunk
        for (let i = 0; i < treeHeight; i++) {
            if (y + i < chunk.height) {
                chunk.setBlock(x, y + i, z, 4); // Wood
            }
        }
        
        // Generate leaves
        const leavesY = y + treeHeight - 1;
        for (let dx = -2; dx <= 2; dx++) {
            for (let dy = -1; dy <= 1; dy++) {
                for (let dz = -2; dz <= 2; dz++) {
                    const leafX = x + dx;
                    const leafY = leavesY + dy;
                    const leafZ = z + dz;
                    
                    if (chunk.isValidCoordinate(leafX, leafY, leafZ)) {
                        const distance = Math.abs(dx) + Math.abs(dy) + Math.abs(dz);
                        if (distance <= 3 && chunk.getBlock(leafX, leafY, leafZ) === 0) {
                            if (this.random(leafX * 10, leafY * 10, leafZ * 10) < 0.8) {
                                chunk.setBlock(leafX, leafY, leafZ, 14); // Leaves
                            }
                        }
                    }
                }
            }
        }
    }

    // Simple noise function (pseudo-random)
    noise(x, z) {
        const n = Math.sin(x * 12.9898 + z * 78.233 + this.seed) * 43758.5453;
        return (n - Math.floor(n)) * 2 - 1; // Range: -1 to 1
    }

    // 3D noise function
    noise3D(x, y, z) {
        const n = Math.sin(x * 12.9898 + y * 78.233 + z * 37.719 + this.seed) * 43758.5453;
        return (n - Math.floor(n)) * 2 - 1; // Range: -1 to 1
    }

    // Random function with seed
    random(x, y, z) {
        const n = Math.sin(x * 12.9898 + y * 78.233 + z * 37.719 + this.seed) * 43758.5453;
        return n - Math.floor(n); // Range: 0 to 1
    }

    // Set new seed
    setSeed(newSeed) {
        this.seed = newSeed;
        console.log(`World generator seed changed to: ${this.seed}`);
    }

    // Get current seed
    getSeed() {
        return this.seed;
    }
}
