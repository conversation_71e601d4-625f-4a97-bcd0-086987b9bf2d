/**
 * Chunk Class
 * Represents a 3D section of the world containing blocks
 */

export class Chunk {
    constructor(x, z, size, height) {
        this.x = x; // Chunk X coordinate
        this.z = z; // Chunk Z coordinate
        this.size = size; // Chunk size (width and depth)
        this.height = height; // Chunk height
        
        // 3D array to store block data [x][y][z]
        this.blocks = new Array(size);
        for (let x = 0; x < size; x++) {
            this.blocks[x] = new Array(height);
            for (let y = 0; y < height; y++) {
                this.blocks[x][y] = new Array(size).fill(0); // 0 = air
            }
        }
        
        // Three.js mesh for rendering
        this.mesh = null;
        
        // Chunk state
        this.isGenerated = false;
        this.isDirty = false; // Needs mesh regeneration
        
        // Metadata
        this.lastModified = Date.now();
        this.blockCount = 0; // Number of non-air blocks
    }

    // Get block at local coordinates
    getBlock(x, y, z) {
        if (!this.isValidCoordinate(x, y, z)) {
            return 0; // Air for out-of-bounds
        }
        
        return this.blocks[x][y][z];
    }

    // Set block at local coordinates
    setBlock(x, y, z, blockType) {
        if (!this.isValidCoordinate(x, y, z)) {
            return false;
        }
        
        const oldBlock = this.blocks[x][y][z];
        this.blocks[x][y][z] = blockType;
        
        // Update block count
        if (oldBlock === 0 && blockType !== 0) {
            this.blockCount++;
        } else if (oldBlock !== 0 && blockType === 0) {
            this.blockCount--;
        }
        
        // Mark as dirty for mesh regeneration
        this.isDirty = true;
        this.lastModified = Date.now();
        
        return true;
    }

    // Check if coordinates are valid within this chunk
    isValidCoordinate(x, y, z) {
        return x >= 0 && x < this.size &&
               y >= 0 && y < this.height &&
               z >= 0 && z < this.size;
    }

    // Get world coordinates for a local position
    getWorldPosition(localX, localY, localZ) {
        return {
            x: this.x * this.size + localX,
            y: localY,
            z: this.z * this.size + localZ
        };
    }

    // Get local coordinates from world position
    getLocalPosition(worldX, worldY, worldZ) {
        return {
            x: worldX - this.x * this.size,
            y: worldY,
            z: worldZ - this.z * this.size
        };
    }

    // Fill a region with a specific block type
    fillRegion(startX, startY, startZ, endX, endY, endZ, blockType) {
        const minX = Math.max(0, Math.min(startX, endX));
        const maxX = Math.min(this.size - 1, Math.max(startX, endX));
        const minY = Math.max(0, Math.min(startY, endY));
        const maxY = Math.min(this.height - 1, Math.max(startY, endY));
        const minZ = Math.max(0, Math.min(startZ, endZ));
        const maxZ = Math.min(this.size - 1, Math.max(startZ, endZ));
        
        for (let x = minX; x <= maxX; x++) {
            for (let y = minY; y <= maxY; y++) {
                for (let z = minZ; z <= maxZ; z++) {
                    this.setBlock(x, y, z, blockType);
                }
            }
        }
    }

    // Fill a layer with a specific block type
    fillLayer(y, blockType) {
        if (y < 0 || y >= this.height) return;
        
        for (let x = 0; x < this.size; x++) {
            for (let z = 0; z < this.size; z++) {
                this.setBlock(x, y, z, blockType);
            }
        }
    }

    // Get the highest non-air block at x, z coordinates
    getHeightAt(x, z) {
        if (x < 0 || x >= this.size || z < 0 || z >= this.size) {
            return -1;
        }
        
        for (let y = this.height - 1; y >= 0; y--) {
            if (this.blocks[x][y][z] !== 0) {
                return y;
            }
        }
        
        return -1; // No blocks found
    }

    // Check if the chunk is empty (all air blocks)
    isEmpty() {
        return this.blockCount === 0;
    }

    // Get all blocks of a specific type
    getBlocksOfType(blockType) {
        const blocks = [];
        
        for (let x = 0; x < this.size; x++) {
            for (let y = 0; y < this.height; y++) {
                for (let z = 0; z < this.size; z++) {
                    if (this.blocks[x][y][z] === blockType) {
                        blocks.push({ x, y, z });
                    }
                }
            }
        }
        
        return blocks;
    }

    // Count blocks of a specific type
    countBlocksOfType(blockType) {
        let count = 0;
        
        for (let x = 0; x < this.size; x++) {
            for (let y = 0; y < this.height; y++) {
                for (let z = 0; z < this.size; z++) {
                    if (this.blocks[x][y][z] === blockType) {
                        count++;
                    }
                }
            }
        }
        
        return count;
    }

    // Replace all blocks of one type with another
    replaceBlocks(fromType, toType) {
        let replacedCount = 0;
        
        for (let x = 0; x < this.size; x++) {
            for (let y = 0; y < this.height; y++) {
                for (let z = 0; z < this.size; z++) {
                    if (this.blocks[x][y][z] === fromType) {
                        this.setBlock(x, y, z, toType);
                        replacedCount++;
                    }
                }
            }
        }
        
        return replacedCount;
    }

    // Clear all blocks (set to air)
    clear() {
        for (let x = 0; x < this.size; x++) {
            for (let y = 0; y < this.height; y++) {
                for (let z = 0; z < this.size; z++) {
                    this.blocks[x][y][z] = 0;
                }
            }
        }
        
        this.blockCount = 0;
        this.isDirty = true;
        this.lastModified = Date.now();
    }

    // Get chunk data for serialization
    serialize() {
        return {
            x: this.x,
            z: this.z,
            size: this.size,
            height: this.height,
            blocks: this.blocks,
            isGenerated: this.isGenerated,
            lastModified: this.lastModified,
            blockCount: this.blockCount
        };
    }

    // Load chunk data from serialized format
    deserialize(data) {
        this.x = data.x;
        this.z = data.z;
        this.size = data.size;
        this.height = data.height;
        this.blocks = data.blocks;
        this.isGenerated = data.isGenerated;
        this.lastModified = data.lastModified;
        this.blockCount = data.blockCount;
        this.isDirty = true; // Need to regenerate mesh
    }

    // Get chunk statistics
    getStats() {
        const stats = {
            totalBlocks: this.size * this.height * this.size,
            nonAirBlocks: this.blockCount,
            airBlocks: (this.size * this.height * this.size) - this.blockCount,
            fillPercentage: (this.blockCount / (this.size * this.height * this.size)) * 100,
            lastModified: this.lastModified,
            isGenerated: this.isGenerated,
            isDirty: this.isDirty
        };
        
        return stats;
    }

    // Get neighboring block coordinates (6-directional)
    getNeighbors(x, y, z) {
        const neighbors = [];
        const directions = [
            { x: 1, y: 0, z: 0 },  // Right
            { x: -1, y: 0, z: 0 }, // Left
            { x: 0, y: 1, z: 0 },  // Up
            { x: 0, y: -1, z: 0 }, // Down
            { x: 0, y: 0, z: 1 },  // Forward
            { x: 0, y: 0, z: -1 }  // Back
        ];
        
        for (const dir of directions) {
            const nx = x + dir.x;
            const ny = y + dir.y;
            const nz = z + dir.z;
            
            neighbors.push({
                x: nx,
                y: ny,
                z: nz,
                blockType: this.getBlock(nx, ny, nz),
                isValid: this.isValidCoordinate(nx, ny, nz)
            });
        }
        
        return neighbors;
    }

    // Mark chunk as generated
    markAsGenerated() {
        this.isGenerated = true;
        this.lastModified = Date.now();
    }

    // Mark chunk as clean (mesh updated)
    markAsClean() {
        this.isDirty = false;
    }

    // Get chunk identifier string
    getKey() {
        return `${this.x},${this.z}`;
    }

    // Dispose of resources
    dispose() {
        if (this.mesh) {
            if (this.mesh.geometry) {
                this.mesh.geometry.dispose();
            }
            if (this.mesh.material) {
                this.mesh.material.dispose();
            }
            this.mesh = null;
        }
        
        this.blocks = null;
    }
}
