/**
 * Block Types Definition
 * Defines all available block types in the game
 */

export class BlockTypes {
    static blockTypes = new Map();
    static initialized = false;

    static async init() {
        if (this.initialized) return;

        console.log('Initializing Block Types...');

        // Define all block types
        this.defineBlockTypes();

        this.initialized = true;
        console.log(`Initialized ${this.blockTypes.size} block types`);
    }

    static defineBlockTypes() {
        // Air (empty space)
        this.register(0, {
            id: 0,
            name: 'Air',
            color: 0x000000,
            transparent: true,
            opacity: 0,
            solid: false,
            icon: '⬜',
            craftable: false
        });

        // Dirt
        this.register(1, {
            id: 1,
            name: 'Dirt',
            color: 0x8B4513,
            transparent: false,
            opacity: 1,
            solid: true,
            icon: '🟫',
            craftable: false,
            hardness: 1,
            tool: null
        });

        // Grass
        this.register(2, {
            id: 2,
            name: 'Grass',
            color: 0x228B22,
            transparent: false,
            opacity: 1,
            solid: true,
            icon: '🟩',
            craftable: false,
            hardness: 1,
            tool: null
        });

        // Stone
        this.register(3, {
            id: 3,
            name: 'Stone',
            color: 0x808080,
            transparent: false,
            opacity: 1,
            solid: true,
            icon: '🟦',
            craftable: false,
            hardness: 3,
            tool: 'pickaxe'
        });

        // Wood
        this.register(4, {
            id: 4,
            name: 'Wood',
            color: 0xDEB887,
            transparent: false,
            opacity: 1,
            solid: true,
            icon: '🟨',
            craftable: false,
            hardness: 2,
            tool: 'axe'
        });

        // Sand
        this.register(5, {
            id: 5,
            name: 'Sand',
            color: 0xF4A460,
            transparent: false,
            opacity: 1,
            solid: true,
            icon: '🟡',
            craftable: false,
            hardness: 1,
            tool: null
        });

        // Water
        this.register(6, {
            id: 6,
            name: 'Water',
            color: 0x0077BE,
            transparent: true,
            opacity: 0.7,
            solid: false,
            icon: '🟦',
            craftable: false,
            hardness: 0,
            tool: null
        });

        // Brick (crafted from stone)
        this.register(7, {
            id: 7,
            name: 'Brick',
            color: 0xB22222,
            transparent: false,
            opacity: 1,
            solid: true,
            icon: '🟥',
            craftable: true,
            hardness: 4,
            tool: 'pickaxe',
            recipe: {
                ingredients: [{ id: 3, count: 4 }], // 4 stone
                result: { id: 7, count: 1 }
            }
        });

        // Planks (crafted from wood)
        this.register(8, {
            id: 8,
            name: 'Planks',
            color: 0xDEB887,
            transparent: false,
            opacity: 1,
            solid: true,
            icon: '🟫',
            craftable: true,
            hardness: 2,
            tool: 'axe',
            recipe: {
                ingredients: [{ id: 4, count: 1 }], // 1 wood
                result: { id: 8, count: 4 }
            }
        });

        // Glass (crafted from sand)
        this.register(9, {
            id: 9,
            name: 'Glass',
            color: 0xE6E6FA,
            transparent: true,
            opacity: 0.8,
            solid: true,
            icon: '⬜',
            craftable: true,
            hardness: 1,
            tool: null,
            recipe: {
                ingredients: [{ id: 5, count: 1 }], // 1 sand
                result: { id: 9, count: 1 }
            }
        });

        // Coal
        this.register(10, {
            id: 10,
            name: 'Coal',
            color: 0x2F2F2F,
            transparent: false,
            opacity: 1,
            solid: true,
            icon: '⚫',
            craftable: false,
            hardness: 3,
            tool: 'pickaxe'
        });

        // Iron
        this.register(11, {
            id: 11,
            name: 'Iron',
            color: 0xC0C0C0,
            transparent: false,
            opacity: 1,
            solid: true,
            icon: '⚪',
            craftable: false,
            hardness: 5,
            tool: 'pickaxe'
        });

        // Gold
        this.register(12, {
            id: 12,
            name: 'Gold',
            color: 0xFFD700,
            transparent: false,
            opacity: 1,
            solid: true,
            icon: '🟡',
            craftable: false,
            hardness: 4,
            tool: 'pickaxe'
        });

        // Diamond
        this.register(13, {
            id: 13,
            name: 'Diamond',
            color: 0x00FFFF,
            transparent: false,
            opacity: 1,
            solid: true,
            icon: '💎',
            craftable: false,
            hardness: 10,
            tool: 'pickaxe'
        });

        // Leaves
        this.register(14, {
            id: 14,
            name: 'Leaves',
            color: 0x228B22,
            transparent: true,
            opacity: 0.8,
            solid: true,
            icon: '🍃',
            craftable: false,
            hardness: 1,
            tool: null
        });

        // Snow
        this.register(15, {
            id: 15,
            name: 'Snow',
            color: 0xFFFAFA,
            transparent: false,
            opacity: 1,
            solid: true,
            icon: '❄️',
            craftable: false,
            hardness: 1,
            tool: null
        });
    }

    static register(id, blockType) {
        this.blockTypes.set(id, blockType);
    }

    static get(id) {
        return this.blockTypes.get(id) || this.blockTypes.get(0); // Return air if not found
    }

    static getAll() {
        return this.blockTypes;
    }

    static getByName(name) {
        for (const blockType of this.blockTypes.values()) {
            if (blockType.name.toLowerCase() === name.toLowerCase()) {
                return blockType;
            }
        }
        return null;
    }

    static getCraftableBlocks() {
        const craftable = [];
        for (const blockType of this.blockTypes.values()) {
            if (blockType.craftable && blockType.recipe) {
                craftable.push(blockType);
            }
        }
        return craftable;
    }

    static getBlocksForHotbar() {
        // Return blocks that should appear in the hotbar
        return [
            this.get(1), // Dirt
            this.get(2), // Grass
            this.get(3), // Stone
            this.get(4), // Wood
            this.get(5)  // Sand
        ];
    }

    static canCraft(recipe, inventory) {
        if (!recipe || !recipe.ingredients) return false;

        for (const ingredient of recipe.ingredients) {
            const availableCount = inventory.getItemCount(ingredient.id) || 0;
            if (availableCount < ingredient.count) {
                return false;
            }
        }

        return true;
    }

    static getRecipe(blockId) {
        const blockType = this.get(blockId);
        return blockType ? blockType.recipe : null;
    }

    static isTransparent(blockId) {
        const blockType = this.get(blockId);
        return blockType ? blockType.transparent : false;
    }

    static isSolid(blockId) {
        const blockType = this.get(blockId);
        return blockType ? blockType.solid : false;
    }

    static getHardness(blockId) {
        const blockType = this.get(blockId);
        return blockType ? (blockType.hardness || 1) : 1;
    }

    static getRequiredTool(blockId) {
        const blockType = this.get(blockId);
        return blockType ? blockType.tool : null;
    }

    static getBreakTime(blockId, tool = null) {
        const hardness = this.getHardness(blockId);
        const requiredTool = this.getRequiredTool(blockId);
        
        let multiplier = 1;
        
        if (requiredTool && tool !== requiredTool) {
            multiplier = 5; // Takes longer without proper tool
        } else if (tool === requiredTool) {
            multiplier = 0.5; // Faster with proper tool
        }
        
        return hardness * multiplier * 0.5; // Base break time in seconds
    }

    static getColor(blockId) {
        const blockType = this.get(blockId);
        return blockType ? blockType.color : 0x000000;
    }

    static getName(blockId) {
        const blockType = this.get(blockId);
        return blockType ? blockType.name : 'Unknown';
    }

    static getIcon(blockId) {
        const blockType = this.get(blockId);
        return blockType ? blockType.icon : '❓';
    }
}
