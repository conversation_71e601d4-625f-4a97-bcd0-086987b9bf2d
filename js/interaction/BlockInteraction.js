/**
 * Block Interaction System
 * Handles block placement, destruction, and raycasting
 */

import * as THREE from 'three';
import { BlockTypes } from '../world/BlockTypes.js';

export class BlockInteraction {
    constructor(camera, scene, voxelWorld, inventorySystem) {
        this.camera = camera;
        this.scene = scene;
        this.voxelWorld = voxelWorld;
        this.inventorySystem = inventorySystem;
        
        // Raycasting
        this.raycaster = new THREE.Raycaster();
        this.raycastDistance = 5.0; // Maximum reach distance
        
        // Block highlighting
        this.highlightMesh = null;
        this.highlightMaterial = null;
        
        // Current target
        this.currentTarget = null;
        this.currentTargetFace = null;
        
        // Block breaking
        this.isBreaking = false;
        this.breakStartTime = 0;
        this.breakDuration = 0;
        this.breakProgress = 0;
        
        // Visual feedback
        this.breakParticles = [];
        
        console.log('BlockInteraction created');
    }

    async init() {
        console.log('Initializing Block Interaction...');
        
        // Create highlight mesh
        this.createHighlightMesh();
        
        console.log('Block Interaction initialized');
    }

    createHighlightMesh() {
        // Create wireframe cube for highlighting blocks
        const geometry = new THREE.BoxGeometry(1.01, 1.01, 1.01);
        this.highlightMaterial = new THREE.MeshBasicMaterial({
            color: 0xffffff,
            wireframe: true,
            transparent: true,
            opacity: 0.5
        });
        
        this.highlightMesh = new THREE.Mesh(geometry, this.highlightMaterial);
        this.highlightMesh.visible = false;
        this.scene.add(this.highlightMesh);
    }

    update() {
        // Perform raycast to find target block
        this.performRaycast();
        
        // Update highlight
        this.updateHighlight();
        
        // Update block breaking
        this.updateBlockBreaking();
        
        // Update UI
        this.updateUI();
    }

    performRaycast() {
        // Set up raycaster
        const direction = new THREE.Vector3(0, 0, -1);
        direction.applyQuaternion(this.camera.quaternion);
        
        this.raycaster.set(this.camera.position, direction);
        
        // Find intersected blocks
        const target = this.findBlockIntersection();
        
        if (target) {
            this.currentTarget = target;
            this.currentTargetFace = this.getTargetFace(target);
        } else {
            this.currentTarget = null;
            this.currentTargetFace = null;
        }
    }

    findBlockIntersection() {
        const start = this.camera.position.clone();
        const direction = new THREE.Vector3(0, 0, -1);
        direction.applyQuaternion(this.camera.quaternion);
        
        const step = 0.1;
        const maxSteps = Math.floor(this.raycastDistance / step);
        
        for (let i = 0; i < maxSteps; i++) {
            const currentPos = start.clone().addScaledVector(direction, i * step);
            const blockX = Math.floor(currentPos.x);
            const blockY = Math.floor(currentPos.y);
            const blockZ = Math.floor(currentPos.z);
            
            const blockType = this.voxelWorld.getBlock(blockX, blockY, blockZ);
            
            if (blockType !== 0 && blockType !== 6) { // Not air or water
                return {
                    position: new THREE.Vector3(blockX, blockY, blockZ),
                    blockType: blockType,
                    distance: i * step,
                    hitPoint: currentPos
                };
            }
        }
        
        return null;
    }

    getTargetFace(target) {
        if (!target) return null;
        
        const blockCenter = target.position.clone().addScalar(0.5);
        const hitPoint = target.hitPoint;
        const diff = hitPoint.clone().sub(blockCenter);
        
        // Determine which face was hit based on the largest component
        const absX = Math.abs(diff.x);
        const absY = Math.abs(diff.y);
        const absZ = Math.abs(diff.z);
        
        if (absX > absY && absX > absZ) {
            return diff.x > 0 ? 'east' : 'west';
        } else if (absY > absZ) {
            return diff.y > 0 ? 'up' : 'down';
        } else {
            return diff.z > 0 ? 'south' : 'north';
        }
    }

    updateHighlight() {
        if (this.currentTarget) {
            this.highlightMesh.position.copy(this.currentTarget.position);
            this.highlightMesh.position.addScalar(0.5);
            this.highlightMesh.visible = true;
            
            // Change color based on action
            if (this.isBreaking) {
                this.highlightMaterial.color.setHex(0xff0000); // Red when breaking
            } else {
                this.highlightMaterial.color.setHex(0xffffff); // White when hovering
            }
        } else {
            this.highlightMesh.visible = false;
        }
    }

    updateBlockBreaking() {
        if (this.isBreaking && this.currentTarget) {
            const currentTime = performance.now();
            const elapsed = (currentTime - this.breakStartTime) / 1000;
            this.breakProgress = Math.min(elapsed / this.breakDuration, 1);
            
            // Update highlight opacity based on break progress
            this.highlightMaterial.opacity = 0.5 + (this.breakProgress * 0.5);
            
            if (this.breakProgress >= 1) {
                // Block is broken
                this.completeBlockBreak();
            }
        }
    }

    updateUI() {
        // Update looking at display
        const lookingAtElement = document.getElementById('looking-at');
        if (lookingAtElement) {
            if (this.currentTarget) {
                const blockName = BlockTypes.getName(this.currentTarget.blockType);
                lookingAtElement.textContent = blockName;
            } else {
                lookingAtElement.textContent = 'None';
            }
        }
    }

    breakBlock() {
        if (!this.currentTarget) return;
        
        if (!this.isBreaking) {
            // Start breaking
            this.startBlockBreak();
        }
    }

    startBlockBreak() {
        if (!this.currentTarget) return;
        
        const blockType = this.currentTarget.blockType;
        const tool = null; // TODO: Get current tool from inventory
        
        this.breakDuration = BlockTypes.getBreakTime(blockType, tool);
        this.breakStartTime = performance.now();
        this.breakProgress = 0;
        this.isBreaking = true;
        
        console.log(`Started breaking ${BlockTypes.getName(blockType)} (${this.breakDuration}s)`);
    }

    completeBlockBreak() {
        if (!this.currentTarget) return;
        
        const blockPos = this.currentTarget.position;
        const blockType = this.currentTarget.blockType;
        
        // Remove block from world
        this.voxelWorld.setBlock(blockPos.x, blockPos.y, blockPos.z, 0);
        
        // Add block to inventory
        this.inventorySystem.addItem(blockType, 1);
        
        // Create break particles
        this.createBreakParticles(blockPos, blockType);
        
        // Reset breaking state
        this.isBreaking = false;
        this.breakProgress = 0;
        
        console.log(`Broke ${BlockTypes.getName(blockType)} at (${blockPos.x}, ${blockPos.y}, ${blockPos.z})`);
    }

    placeBlock(hotbarSlot) {
        if (!this.currentTarget || !this.currentTargetFace) return;
        
        // Get block type from hotbar
        const blockType = this.getBlockTypeFromHotbar(hotbarSlot);
        if (!blockType || blockType === 0) return;
        
        // Calculate placement position
        const placePos = this.getPlacementPosition();
        if (!placePos) return;
        
        // Check if position is valid for placement
        if (!this.canPlaceBlockAt(placePos)) return;
        
        // Check if player has the block in inventory
        if (!this.inventorySystem.hasItem(blockType, 1)) return;
        
        // Place block
        this.voxelWorld.setBlock(placePos.x, placePos.y, placePos.z, blockType);
        
        // Remove block from inventory
        this.inventorySystem.removeItem(blockType, 1);
        
        console.log(`Placed ${BlockTypes.getName(blockType)} at (${placePos.x}, ${placePos.y}, ${placePos.z})`);
    }

    getBlockTypeFromHotbar(slot) {
        // Get block type based on hotbar slot
        const hotbarBlocks = BlockTypes.getBlocksForHotbar();
        if (slot >= 0 && slot < hotbarBlocks.length) {
            return hotbarBlocks[slot].id;
        }
        return 0;
    }

    getPlacementPosition() {
        if (!this.currentTarget || !this.currentTargetFace) return null;
        
        const blockPos = this.currentTarget.position.clone();
        
        switch (this.currentTargetFace) {
            case 'up':
                blockPos.y += 1;
                break;
            case 'down':
                blockPos.y -= 1;
                break;
            case 'north':
                blockPos.z -= 1;
                break;
            case 'south':
                blockPos.z += 1;
                break;
            case 'west':
                blockPos.x -= 1;
                break;
            case 'east':
                blockPos.x += 1;
                break;
        }
        
        return blockPos;
    }

    canPlaceBlockAt(position) {
        // Check if position is already occupied
        const existingBlock = this.voxelWorld.getBlock(position.x, position.y, position.z);
        if (existingBlock !== 0 && existingBlock !== 6) { // Not air or water
            return false;
        }
        
        // Check if position would intersect with player
        // TODO: Add player collision check
        
        return true;
    }

    createBreakParticles(blockPos, blockType) {
        // Create simple particle effect for block breaking
        const particleCount = 10;
        const blockColor = BlockTypes.getColor(blockType);
        
        for (let i = 0; i < particleCount; i++) {
            const particle = {
                position: new THREE.Vector3(
                    blockPos.x + 0.5 + (Math.random() - 0.5) * 0.5,
                    blockPos.y + 0.5 + (Math.random() - 0.5) * 0.5,
                    blockPos.z + 0.5 + (Math.random() - 0.5) * 0.5
                ),
                velocity: new THREE.Vector3(
                    (Math.random() - 0.5) * 2,
                    Math.random() * 2,
                    (Math.random() - 0.5) * 2
                ),
                life: 1.0,
                color: blockColor
            };
            
            this.breakParticles.push(particle);
        }
        
        // Remove particles after a short time
        setTimeout(() => {
            this.breakParticles.splice(0, particleCount);
        }, 1000);
    }

    stopBreaking() {
        this.isBreaking = false;
        this.breakProgress = 0;
    }

    // Getters
    getCurrentTarget() {
        return this.currentTarget;
    }

    getCurrentTargetFace() {
        return this.currentTargetFace;
    }

    isCurrentlyBreaking() {
        return this.isBreaking;
    }

    getBreakProgress() {
        return this.breakProgress;
    }

    // Debug methods
    getDebugInfo() {
        return {
            currentTarget: this.currentTarget,
            currentTargetFace: this.currentTargetFace,
            isBreaking: this.isBreaking,
            breakProgress: this.breakProgress,
            particleCount: this.breakParticles.length
        };
    }
}
