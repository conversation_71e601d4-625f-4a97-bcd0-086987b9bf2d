<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Minecraft-like Game - Three.js</title>
    <link rel="stylesheet" href="styles/main.css">
</head>
<body>
    <div id="game-container">
        <!-- Loading Screen -->
        <div id="loading-screen">
            <div class="loading-content">
                <h1>Minecraft-like Game</h1>
                <div class="loading-bar">
                    <div class="loading-progress"></div>
                </div>
                <p>Loading...</p>
            </div>
        </div>

        <!-- Game Canvas -->
        <canvas id="game-canvas"></canvas>

        <!-- Game UI -->
        <div id="game-ui" style="display: none;">
            <!-- Crosshair -->
            <div id="crosshair">
                <div class="crosshair-line horizontal"></div>
                <div class="crosshair-line vertical"></div>
            </div>

            <!-- HUD -->
            <div id="hud">
                <div id="debug-info">
                    <div>FPS: <span id="fps">0</span></div>
                    <div>Position: <span id="position">0, 0, 0</span></div>
                    <div>Looking at: <span id="looking-at">None</span></div>
                </div>
            </div>

            <!-- Hotbar -->
            <div id="hotbar">
                <div class="hotbar-slot active" data-slot="0">
                    <div class="slot-content">
                        <span class="block-icon">🟫</span>
                        <span class="slot-number">1</span>
                    </div>
                </div>
                <div class="hotbar-slot" data-slot="1">
                    <div class="slot-content">
                        <span class="block-icon">🟩</span>
                        <span class="slot-number">2</span>
                    </div>
                </div>
                <div class="hotbar-slot" data-slot="2">
                    <div class="slot-content">
                        <span class="block-icon">🟦</span>
                        <span class="slot-number">3</span>
                    </div>
                </div>
                <div class="hotbar-slot" data-slot="3">
                    <div class="slot-content">
                        <span class="block-icon">🟥</span>
                        <span class="slot-number">4</span>
                    </div>
                </div>
                <div class="hotbar-slot" data-slot="4">
                    <div class="slot-content">
                        <span class="block-icon">🟨</span>
                        <span class="slot-number">5</span>
                    </div>
                </div>
            </div>

            <!-- Inventory Panel -->
            <div id="inventory-panel" style="display: none;">
                <div class="inventory-header">
                    <h3>Inventory</h3>
                    <button id="close-inventory">×</button>
                </div>
                <div class="inventory-grid" id="inventory-grid">
                    <!-- Inventory slots will be generated dynamically -->
                </div>
            </div>

            <!-- Crafting Panel -->
            <div id="crafting-panel" style="display: none;">
                <div class="crafting-header">
                    <h3>Crafting</h3>
                    <button id="close-crafting">×</button>
                </div>
                <div class="crafting-content">
                    <div class="crafting-grid">
                        <div class="crafting-input">
                            <div class="crafting-slot" data-slot="0"></div>
                            <div class="crafting-slot" data-slot="1"></div>
                            <div class="crafting-slot" data-slot="2"></div>
                            <div class="crafting-slot" data-slot="3"></div>
                        </div>
                        <div class="crafting-arrow">→</div>
                        <div class="crafting-output">
                            <div class="crafting-result-slot"></div>
                        </div>
                    </div>
                    <div class="recipe-list">
                        <h4>Available Recipes</h4>
                        <div id="recipe-list-content">
                            <!-- Recipes will be populated dynamically -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Game Menu -->
        <div id="game-menu" style="display: none;">
            <div class="menu-content">
                <h2>Game Menu</h2>
                <button id="resume-game">Resume Game</button>
                <button id="show-controls">Controls</button>
                <button id="restart-game">Restart Game</button>
            </div>
        </div>

        <!-- Controls Help -->
        <div id="controls-help" style="display: none;">
            <div class="controls-content">
                <h3>Game Controls</h3>
                <div class="controls-list">
                    <div class="control-item">
                        <span class="key">W A S D</span>
                        <span class="description">Move around</span>
                    </div>
                    <div class="control-item">
                        <span class="key">Mouse</span>
                        <span class="description">Look around</span>
                    </div>
                    <div class="control-item">
                        <span class="key">Space</span>
                        <span class="description">Jump</span>
                    </div>
                    <div class="control-item">
                        <span class="key">Left Click</span>
                        <span class="description">Break blocks</span>
                    </div>
                    <div class="control-item">
                        <span class="key">Right Click</span>
                        <span class="description">Place blocks</span>
                    </div>
                    <div class="control-item">
                        <span class="key">E</span>
                        <span class="description">Open inventory</span>
                    </div>
                    <div class="control-item">
                        <span class="key">C</span>
                        <span class="description">Open crafting</span>
                    </div>
                    <div class="control-item">
                        <span class="key">1-5</span>
                        <span class="description">Select hotbar slot</span>
                    </div>
                    <div class="control-item">
                        <span class="key">ESC</span>
                        <span class="description">Open menu</span>
                    </div>
                </div>
                <button id="close-controls">Close</button>
            </div>
        </div>

        <!-- Instructions -->
        <div id="instructions">
            <div class="instructions-content">
                <h2>Welcome to Minecraft-like Game!</h2>
                <p>Click anywhere to start playing. Use WASD to move, mouse to look around.</p>
                <p>Left click to break blocks, right click to place them.</p>
                <p>Press ESC to open the menu at any time.</p>
                <button id="start-game">Start Game</button>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script type="importmap">
        {
            "imports": {
                "three": "https://unpkg.com/three@0.160.0/build/three.module.js",
                "three/addons/": "https://unpkg.com/three@0.160.0/examples/jsm/"
            }
        }
    </script>
    <script type="module" src="js/main.js"></script>
</body>
</html>
