# Minecraft-like Game - Three.js

A browser-based voxel game built with Three.js, featuring block-based world generation, player movement, building mechanics, and crafting system.

## Features

### Core Gameplay
- **Open-world exploration**: Procedurally generated terrain with hills, caves, and resources
- **Block mining**: Break blocks to collect materials
- **Block building**: Place blocks to construct structures
- **Crafting system**: Combine materials to create new items and blocks
- **Inventory management**: Store and organize collected items

### Technical Features
- **Voxel-based world**: Efficient chunk-based rendering system
- **First-person controls**: Smooth WASD movement with mouse look
- **Physics simulation**: Gravity, collision detection, and jumping
- **Real-time lighting**: Dynamic shadows and ambient lighting
- **Responsive UI**: Clean interface that works on different screen sizes

## How to Play

### Getting Started
1. Open the game in a web browser
2. Click "Start Game" to begin
3. Click anywhere to enable mouse controls (pointer lock)

### Controls
- **W A S D**: Move around
- **Mouse**: Look around
- **Space**: Jump
- **Shift**: Run (hold while moving)
- **Left Click**: Break blocks
- **Right Click**: Place blocks
- **Mouse Wheel**: Scroll through hotbar items
- **1-5**: Select hotbar slots directly
- **E**: Open/close inventory
- **C**: Open/close crafting menu
- **ESC**: Open game menu

### Gameplay Mechanics

#### Mining
- Look at a block and left-click to break it
- Different blocks have different hardness levels
- Broken blocks are automatically added to your inventory
- Some blocks require specific tools (future feature)

#### Building
- Select a block type from your hotbar (1-5 keys)
- Right-click on an existing block to place a new block adjacent to it
- You can only place blocks if you have them in your inventory

#### Crafting
- Press **C** to open the crafting interface
- Click on crafting slots to place items (currently cycles through available items)
- Available recipes are shown on the right side
- Click on a recipe to auto-fill the crafting grid
- Click the result slot to craft the item (if you have materials)

#### Inventory
- Press **E** to open your inventory
- View all collected items and their quantities
- Items are automatically organized in slots
- The first 5 slots correspond to your hotbar

### Block Types

#### Basic Blocks
- **Dirt** 🟫: Common ground material
- **Grass** 🟩: Surface blocks with grass texture
- **Stone** 🟦: Hard underground material
- **Wood** 🟨: From trees, used for crafting
- **Sand** 🟡: Found in certain areas

#### Crafted Blocks
- **Planks** 🟫: Crafted from wood (1 wood → 4 planks)
- **Brick** 🟥: Crafted from stone (4 stone → 1 brick)
- **Glass** ⬜: Crafted from sand (1 sand → 1 glass)

#### Resources
- **Coal** ⚫: Fuel and crafting material
- **Iron** ⚪: Metal for tools and advanced items
- **Gold** 🟡: Precious metal
- **Diamond** 💎: Rare and valuable

### Crafting Recipes

#### Basic Recipes
- **Wood → Planks**: 1 Wood = 4 Planks
- **Stone → Brick**: 4 Stone = 1 Brick  
- **Sand → Glass**: 1 Sand = 1 Glass
- **Planks → Sticks**: 2 Planks = 4 Sticks

#### Advanced Recipes (Examples)
- **Stone Pickaxe**: 2 Stone + 1 Stick
- More recipes can be added easily through the modular system

## Technical Implementation

### Architecture
The game is built with a modular architecture:

- **GameEngine**: Core Three.js setup and system coordination
- **VoxelWorld**: Chunk-based world generation and management
- **PlayerController**: First-person movement and physics
- **BlockInteraction**: Raycasting for block placement/destruction
- **InventorySystem**: Item storage and management
- **CraftingSystem**: Recipe handling and item creation
- **UIManager**: User interface and input handling

### Performance Features
- **Chunk-based rendering**: Only visible chunks are rendered
- **Efficient mesh generation**: Face culling for hidden block faces
- **Dynamic loading**: Chunks load/unload based on player position
- **Optimized materials**: Shared materials for block types

### Browser Compatibility
- Modern browsers with WebGL support
- ES6 modules support required
- Pointer Lock API for mouse controls

## Development

### Running Locally
1. Clone or download the project
2. Start a local web server in the project directory:
   ```bash
   python3 -m http.server 8000
   ```
3. Open `http://localhost:8000` in your browser

### Project Structure
```
game001/
├── index.html              # Main HTML file
├── styles/
│   └── main.css            # Game styling
├── js/
│   ├── main.js             # Entry point
│   ├── engine/
│   │   └── GameEngine.js   # Core game engine
│   ├── world/
│   │   ├── VoxelWorld.js   # World management
│   │   ├── Chunk.js        # Chunk data structure
│   │   ├── BlockTypes.js   # Block definitions
│   │   └── WorldGenerator.js # Terrain generation
│   ├── player/
│   │   └── PlayerController.js # Player movement
│   ├── interaction/
│   │   └── BlockInteraction.js # Block placement/breaking
│   ├── inventory/
│   │   └── InventorySystem.js # Item management
│   ├── crafting/
│   │   └── CraftingSystem.js # Recipe system
│   ├── ui/
│   │   └── UIManager.js    # User interface
│   └── input/
│       └── InputManager.js # Input handling
└── README.md               # This file
```

### Extending the Game
The modular architecture makes it easy to add new features:

- **New block types**: Add to `BlockTypes.js`
- **New recipes**: Add to `CraftingSystem.js`
- **New world features**: Modify `WorldGenerator.js`
- **New UI elements**: Extend `UIManager.js`

## Future Enhancements

### Planned Features
- **Tools and weapons**: Pickaxes, swords, etc.
- **Advanced crafting**: Workbenches, furnaces
- **Multiplayer support**: Real-time collaboration
- **Save/load system**: Persistent worlds
- **More biomes**: Different terrain types
- **Mobs and NPCs**: Interactive entities
- **Sound effects**: Audio feedback
- **Texture system**: Proper block textures

### Performance Improvements
- **Web Workers**: Background chunk generation
- **Level of Detail**: Distance-based rendering optimization
- **Occlusion culling**: Hide non-visible chunks
- **Instanced rendering**: Optimize repeated geometry

## Credits

Built with:
- **Three.js**: 3D graphics library
- **Modern JavaScript**: ES6 modules and features
- **HTML5 Canvas**: Rendering surface
- **CSS3**: Styling and animations

## License

This project is created for educational purposes. Feel free to use and modify as needed.

---

**Enjoy building and exploring your voxel world!** 🎮⛏️🏗️
